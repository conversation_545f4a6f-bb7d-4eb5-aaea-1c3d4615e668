﻿@model THLTW_B2.Models.Product
@{
    ViewData["Title"] = "Chi tiết sản phẩm";
}

<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h3><i class="fas fa-eye"></i> @ViewData["Title"]</h3>
                    @if (User.IsInRole("Admin"))
                    {
                        <div class="btn-group">
                            <a asp-action="Update" asp-route-id="@Model.Id" class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i> Chỉnh sửa
                            </a>
                            <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger btn-sm">
                                <i class="fas fa-trash"></i> Xóa
                            </a>
                        </div>
                    }
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-5">
                        @if (!string.IsNullOrEmpty(Model.ImageUrl))
                        {
                            <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid rounded shadow" style="width: 100%; max-height: 400px; object-fit: cover;" />
                        }
                        else
                        {
                            <div class="bg-light d-flex align-items-center justify-content-center rounded shadow" style="height: 400px;">
                                <div class="text-center">
                                    <i class="fas fa-image text-muted" style="font-size: 4rem;"></i>
                                    <p class="text-muted mt-2">Chưa có hình ảnh</p>
                                </div>
                            </div>
                        }
                    </div>
                    <div class="col-md-7">
                        <div class="product-info">
                            <h2 class="mb-3">@Model.Name</h2>

                            <div class="mb-3">
                                <span class="text-success fw-bold" style="font-size: 1.5rem;">
                                    @Model.Price.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))
                                </span>
                            </div>

                            <div class="mb-3">
                                <strong>Danh mục:</strong>
                                @if (Model.Category != null)
                                {
                                    <span class="badge bg-primary ms-2">@Model.Category.Name</span>
                                }
                                else
                                {
                                    <span class="text-muted ms-2">Chưa phân loại</span>
                                }
                            </div>

                            <div class="mb-4">
                                <strong>Mô tả sản phẩm:</strong>
                                <div class="mt-2 p-3 bg-light rounded">
                                    @if (!string.IsNullOrEmpty(Model.Description))
                                    {
                                        <p class="mb-0">@Model.Description</p>
                                    }
                                    else
                                    {
                                        <p class="mb-0 text-muted">Chưa có mô tả cho sản phẩm này.</p>
                                    }
                                </div>
                            </div>

                            <div class="product-actions">
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Quay lại danh sách
                                </a>
                                @if (User.IsInRole("Admin"))
                                {
                                    <a asp-action="Update" asp-route-id="@Model.Id" class="btn btn-warning">
                                        <i class="fas fa-edit"></i> Chỉnh sửa
                                    </a>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>