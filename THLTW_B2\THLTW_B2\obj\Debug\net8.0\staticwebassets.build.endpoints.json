{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "Identity/css/site.css", "AssetFile": "Identity/css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1592"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"iIBjbvm5vITl1MTy8HKNN3NBvO3WBn8nJD7zOoyqoaU=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iIBjbvm5vITl1MTy8HKNN3NBvO3WBn8nJD7zOoyqoaU="}]}, {"Route": "Identity/favicon.ico", "AssetFile": "Identity/favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "32038"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qU+KhVPK6oQw3UyjzAHU4xjRmCj3TLZUU/+39dni9E0="}]}, {"Route": "Identity/js/site.js", "AssetFile": "Identity/js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "231"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo="}]}, {"Route": "Identity/lib/bootstrap/LICENSE", "AssetFile": "Identity/lib/bootstrap/LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "70538"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "196535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51319"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "117439"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "70612"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "196539"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "117516"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5850"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "105138"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4646"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "35330"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5827"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "105151"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4718"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "41570"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "71584"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "192271"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53479"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "111875"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "71451"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "192214"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53407"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "111710"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "204136"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "536547"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "203803"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "536461"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162825"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU="}]}, {"Route": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "Identity/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "661035"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "208492"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "425643"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "78468"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "327261"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "139019"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "288320"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "72016"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "222508"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "148168"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "289522"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "59511"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM="}]}, {"Route": "Identity/lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "Identity/lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "217145"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88="}]}, {"Route": "Identity/lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "Identity/lib/jquery-validation-unobtrusive/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "Identity/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetFile": "Identity/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "Identity/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetFile": "Identity/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "Identity/lib/jquery-validation/LICENSE.md", "AssetFile": "Identity/lib/jquery-validation/LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "Identity/lib/jquery-validation/dist/additional-methods.js", "AssetFile": "Identity/lib/jquery-validation/dist/additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "52977"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E="}]}, {"Route": "Identity/lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "Identity/lib/jquery-validation/dist/additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc="}]}, {"Route": "Identity/lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "Identity/lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="}]}, {"Route": "Identity/lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "Identity/lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}]}, {"Route": "Identity/lib/jquery/LICENSE.txt", "AssetFile": "Identity/lib/jquery/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "Identity/lib/jquery/dist/jquery.js", "AssetFile": "Identity/lib/jquery/dist/jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "288580"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk="}]}, {"Route": "Identity/lib/jquery/dist/jquery.min.js", "AssetFile": "Identity/lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "89501"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4="}]}, {"Route": "Identity/lib/jquery/dist/jquery.min.map", "AssetFile": "Identity/lib/jquery/dist/jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "137972"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=\""}, {"Name": "Last-Modified", "Value": "Thu, 18 Jan 2024 14:12:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I="}]}, {"Route": "THLTW_B2.hx2tgkuc5c.styles.css", "AssetFile": "THLTW_B2.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1126"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"FXQXUqjm5ItLeGBjigzX1qkZTOM7PJFcOFRepIqpOOI=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:56:12 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hx2tgkuc5c"}, {"Name": "integrity", "Value": "sha256-FXQXUqjm5ItLeGBjigzX1qkZTOM7PJFcOFRepIqpOOI="}, {"Name": "label", "Value": "THLTW_B2.styles.css"}]}, {"Route": "THLTW_B2.styles.css", "AssetFile": "THLTW_B2.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1126"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"FXQXUqjm5ItLeGBjigzX1qkZTOM7PJFcOFRepIqpOOI=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:56:12 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FXQXUqjm5ItLeGBjigzX1qkZTOM7PJFcOFRepIqpOOI="}]}, {"Route": "Temp/css/bootstrap.css", "AssetFile": "Temp/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "192348"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Nfu23DiRqsrx/6B6vsI0T9vEVKq1M6KgO8+TV363g3s=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nfu23DiRqsrx/6B6vsI0T9vEVKq1M6KgO8+TV363g3s="}]}, {"Route": "Temp/css/bootstrap.zuvuklkj3l.css", "AssetFile": "Temp/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "192348"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Nfu23DiRqsrx/6B6vsI0T9vEVKq1M6KgO8+TV363g3s=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zuvuklkj3l"}, {"Name": "integrity", "Value": "sha256-Nfu23DiRqsrx/6B6vsI0T9vEVKq1M6KgO8+TV363g3s="}, {"Name": "label", "Value": "Temp/css/bootstrap.css"}]}, {"Route": "Temp/css/responsive.css", "AssetFile": "Temp/css/responsive.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3098"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wJ3uzx8NXJIHtbB1o+5HHawgjtLqkHdX2fVnfbkBMgo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wJ3uzx8NXJIHtbB1o+5HHawgjtLqkHdX2fVnfbkBMgo="}]}, {"Route": "Temp/css/responsive.o69prm98h9.css", "AssetFile": "Temp/css/responsive.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3098"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wJ3uzx8NXJIHtbB1o+5HHawgjtLqkHdX2fVnfbkBMgo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o69prm98h9"}, {"Name": "integrity", "Value": "sha256-wJ3uzx8NXJIHtbB1o+5HHawgjtLqkHdX2fVnfbkBMgo="}, {"Name": "label", "Value": "Temp/css/responsive.css"}]}, {"Route": "Temp/css/style.1j2f1gxnrx.css", "AssetFile": "Temp/css/style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19292"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jWsGxKDi+z5M0vHnsU4tFmL9f/GRf7yVY9dvcRzzqMk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1j2f1gxnrx"}, {"Name": "integrity", "Value": "sha256-jWsGxKDi+z5M0vHnsU4tFmL9f/GRf7yVY9dvcRzzqMk="}, {"Name": "label", "Value": "Temp/css/style.css"}]}, {"Route": "Temp/css/style.css", "AssetFile": "Temp/css/style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19292"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jWsGxKDi+z5M0vHnsU4tFmL9f/GRf7yVY9dvcRzzqMk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jWsGxKDi+z5M0vHnsU4tFmL9f/GRf7yVY9dvcRzzqMk="}]}, {"Route": "Temp/css/style.css.gvu7i7k5kc.map", "AssetFile": "Temp/css/style.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15085"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9BkqyCGittrNiV6vfWVHWdK+YGj4h4ZkID6X5wjfWLY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gvu7i7k5kc"}, {"Name": "integrity", "Value": "sha256-9BkqyCGittrNiV6vfWVHWdK+YGj4h4ZkID6X5wjfWLY="}, {"Name": "label", "Value": "Temp/css/style.css.map"}]}, {"Route": "Temp/css/style.css.map", "AssetFile": "Temp/css/style.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15085"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9BkqyCGittrNiV6vfWVHWdK+YGj4h4ZkID6X5wjfWLY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9BkqyCGittrNiV6vfWVHWdK+YGj4h4ZkID6X5wjfWLY="}]}, {"Route": "Temp/css/style.pfe0i9gtei.scss", "AssetFile": "Temp/css/style.scss", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14521"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"FZ9R1Wgcmh9Z6cAOIecF8VXcfEw3kQidx7pee2B+BQY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pfe0i9gtei"}, {"Name": "integrity", "Value": "sha256-FZ9R1Wgcmh9Z6cAOIecF8VXcfEw3kQidx7pee2B+BQY="}, {"Name": "label", "Value": "Temp/css/style.scss"}]}, {"Route": "Temp/css/style.scss", "AssetFile": "Temp/css/style.scss", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14521"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"FZ9R1Wgcmh9Z6cAOIecF8VXcfEw3kQidx7pee2B+BQY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FZ9R1Wgcmh9Z6cAOIecF8VXcfEw3kQidx7pee2B+BQY="}]}, {"Route": "Temp/images/agency-img.4tuo3sb2as.jpg", "AssetFile": "Temp/images/agency-img.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "161753"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"+LS08PT/iUtfBwsb8rDFLVcS4dDBawoCXy06HHXl2tc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4tuo3sb2as"}, {"Name": "integrity", "Value": "sha256-+LS08PT/iUtfBwsb8rDFLVcS4dDBawoCXy06HHXl2tc="}, {"Name": "label", "Value": "Temp/images/agency-img.jpg"}]}, {"Route": "Temp/images/agency-img.jpg", "AssetFile": "Temp/images/agency-img.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "161753"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"+LS08PT/iUtfBwsb8rDFLVcS4dDBawoCXy06HHXl2tc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+LS08PT/iUtfBwsb8rDFLVcS4dDBawoCXy06HHXl2tc="}]}, {"Route": "Temp/images/call.lii4ryyugf.png", "AssetFile": "Temp/images/call.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1273"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Q2YXx2Z2PgaBXZl59Ba//rthYeHcLsLa4okESBj+ssM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lii4ryyugf"}, {"Name": "integrity", "Value": "sha256-Q2YXx2Z2PgaBXZl59Ba//rthYeHcLsLa4okESBj+ssM="}, {"Name": "label", "Value": "Temp/images/call.png"}]}, {"Route": "Temp/images/call.png", "AssetFile": "Temp/images/call.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "1273"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Q2YXx2Z2PgaBXZl59Ba//rthYeHcLsLa4okESBj+ssM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Q2YXx2Z2PgaBXZl59Ba//rthYeHcLsLa4okESBj+ssM="}]}, {"Route": "Temp/images/client-bg.dzgr2pc4b5.png", "AssetFile": "Temp/images/client-bg.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1044"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"uZRdoG0BmnZZ4gOWpVal2AhieAhYl+rF5HVjwIvqNlI=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dzgr2pc4b5"}, {"Name": "integrity", "Value": "sha256-uZRdoG0BmnZZ4gOWpVal2AhieAhYl+rF5HVjwIvqNlI="}, {"Name": "label", "Value": "Temp/images/client-bg.png"}]}, {"Route": "Temp/images/client-bg.png", "AssetFile": "Temp/images/client-bg.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "1044"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"uZRdoG0BmnZZ4gOWpVal2AhieAhYl+rF5HVjwIvqNlI=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uZRdoG0BmnZZ4gOWpVal2AhieAhYl+rF5HVjwIvqNlI="}]}, {"Route": "Temp/images/client.j1sl3nve4n.jpg", "AssetFile": "Temp/images/client.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "29653"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"yW/kRrbFUdXfr31DrnYesHwK1voL9R5JGYbNcCWh6PA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j1sl3nve4n"}, {"Name": "integrity", "Value": "sha256-yW/kRrbFUdXfr31DrnYesHwK1voL9R5JGYbNcCWh6PA="}, {"Name": "label", "Value": "Temp/images/client.jpg"}]}, {"Route": "Temp/images/client.jpg", "AssetFile": "Temp/images/client.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "29653"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"yW/kRrbFUdXfr31DrnYesHwK1voL9R5JGYbNcCWh6PA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yW/kRrbFUdXfr31DrnYesHwK1voL9R5JGYbNcCWh6PA="}]}, {"Route": "Temp/images/fb.cpfhyj3vvs.png", "AssetFile": "Temp/images/fb.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1237"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"wOcTgSPOkAx5LEX6o2EFHUPfQU3vr2QS7kB18T6PgP4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cpfhyj3vvs"}, {"Name": "integrity", "Value": "sha256-wOcTgSPOkAx5LEX6o2EFHUPfQU3vr2QS7kB18T6PgP4="}, {"Name": "label", "Value": "Temp/images/fb.png"}]}, {"Route": "Temp/images/fb.png", "AssetFile": "Temp/images/fb.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "1237"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"wOcTgSPOkAx5LEX6o2EFHUPfQU3vr2QS7kB18T6PgP4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wOcTgSPOkAx5LEX6o2EFHUPfQU3vr2QS7kB18T6PgP4="}]}, {"Route": "Temp/images/l1.gndwq0zpp0.jpg", "AssetFile": "Temp/images/l1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8691"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"5ChSPjtBPHSk0q5JBAfUotHNqQkWGaWMwpm9uuts/4E=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gndwq0zpp0"}, {"Name": "integrity", "Value": "sha256-5ChSPjtBPHSk0q5JBAfUotHNqQkWGaWMwpm9uuts/4E="}, {"Name": "label", "Value": "Temp/images/l1.jpg"}]}, {"Route": "Temp/images/l1.jpg", "AssetFile": "Temp/images/l1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "8691"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"5ChSPjtBPHSk0q5JBAfUotHNqQkWGaWMwpm9uuts/4E=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5ChSPjtBPHSk0q5JBAfUotHNqQkWGaWMwpm9uuts/4E="}]}, {"Route": "Temp/images/l2.hjqry88bv7.jpg", "AssetFile": "Temp/images/l2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14263"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"436vuhvjvJn9BkR7/WD4QTUVUL1OkuDDtlmKMQ/jxVg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hjqry88bv7"}, {"Name": "integrity", "Value": "sha256-436vuhvjvJn9BkR7/WD4QTUVUL1OkuDDtlmKMQ/jxVg="}, {"Name": "label", "Value": "Temp/images/l2.jpg"}]}, {"Route": "Temp/images/l2.jpg", "AssetFile": "Temp/images/l2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "14263"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"436vuhvjvJn9BkR7/WD4QTUVUL1OkuDDtlmKMQ/jxVg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-436vuhvjvJn9BkR7/WD4QTUVUL1OkuDDtlmKMQ/jxVg="}]}, {"Route": "Temp/images/l3.f7fvfanwlc.jpg", "AssetFile": "Temp/images/l3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15439"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"RVrH06o6NmGRQMa4vZvh0ADksAvMAf92fEqSHGHdAMs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f7fvfanwlc"}, {"Name": "integrity", "Value": "sha256-RVrH06o6NmGRQMa4vZvh0ADksAvMAf92fEqSHGHdAMs="}, {"Name": "label", "Value": "Temp/images/l3.jpg"}]}, {"Route": "Temp/images/l3.jpg", "AssetFile": "Temp/images/l3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "15439"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"RVrH06o6NmGRQMa4vZvh0ADksAvMAf92fEqSHGHdAMs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RVrH06o6NmGRQMa4vZvh0ADksAvMAf92fEqSHGHdAMs="}]}, {"Route": "Temp/images/l4.64qlwne2rr.jpg", "AssetFile": "Temp/images/l4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19086"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"drNut3cMSDlKU1gW4WXJ/QsVDQXtnDps4NNN5oA/4rY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "64qlwne2rr"}, {"Name": "integrity", "Value": "sha256-drNut3cMSDlKU1gW4WXJ/QsVDQXtnDps4NNN5oA/4rY="}, {"Name": "label", "Value": "Temp/images/l4.jpg"}]}, {"Route": "Temp/images/l4.jpg", "AssetFile": "Temp/images/l4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "19086"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"drNut3cMSDlKU1gW4WXJ/QsVDQXtnDps4NNN5oA/4rY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-drNut3cMSDlKU1gW4WXJ/QsVDQXtnDps4NNN5oA/4rY="}]}, {"Route": "Temp/images/l5.deiexd9265.jpg", "AssetFile": "Temp/images/l5.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12063"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"pXmARTkFL4g+XanBiMPrH62xl9lkb5BWHlZgcjK+iXg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "deiexd9265"}, {"Name": "integrity", "Value": "sha256-pXmARTkFL4g+XanBiMPrH62xl9lkb5BWHlZgcjK+iXg="}, {"Name": "label", "Value": "Temp/images/l5.jpg"}]}, {"Route": "Temp/images/l5.jpg", "AssetFile": "Temp/images/l5.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "12063"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"pXmARTkFL4g+XanBiMPrH62xl9lkb5BWHlZgcjK+iXg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pXmARTkFL4g+XanBiMPrH62xl9lkb5BWHlZgcjK+iXg="}]}, {"Route": "Temp/images/l6.3euxj82o14.jpg", "AssetFile": "Temp/images/l6.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "20869"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"G0upsJCtg7tteqH0ezMWqE8TpvoycWfLUBj0YTJx8M0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3euxj82o14"}, {"Name": "integrity", "Value": "sha256-G0upsJCtg7tteqH0ezMWqE8TpvoycWfLUBj0YTJx8M0="}, {"Name": "label", "Value": "Temp/images/l6.jpg"}]}, {"Route": "Temp/images/l6.jpg", "AssetFile": "Temp/images/l6.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "20869"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"G0upsJCtg7tteqH0ezMWqE8TpvoycWfLUBj0YTJx8M0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G0upsJCtg7tteqH0ezMWqE8TpvoycWfLUBj0YTJx8M0="}]}, {"Route": "Temp/images/line.png", "AssetFile": "Temp/images/line.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "301"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"cdi549LyjwOokCdgrCUsXjQN2zIsyqiNf3mtsBTl1Z4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cdi549LyjwOokCdgrCUsXjQN2zIsyqiNf3mtsBTl1Z4="}]}, {"Route": "Temp/images/line.r5cgab3c13.png", "AssetFile": "Temp/images/line.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "301"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"cdi549LyjwOokCdgrCUsXjQN2zIsyqiNf3mtsBTl1Z4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r5cgab3c13"}, {"Name": "integrity", "Value": "sha256-cdi549LyjwOokCdgrCUsXjQN2zIsyqiNf3mtsBTl1Z4="}, {"Name": "label", "Value": "Temp/images/line.png"}]}, {"Route": "Temp/images/link.j52yqk8wfl.png", "AssetFile": "Temp/images/link.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "301"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"mRYlVl4c+XDmuCH3K+H8TdsWnKukPgJwHQQkw/UcOEY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j52yqk8wfl"}, {"Name": "integrity", "Value": "sha256-mRYlVl4c+XDmuCH3K+H8TdsWnKukPgJwHQQkw/UcOEY="}, {"Name": "label", "Value": "Temp/images/link.png"}]}, {"Route": "Temp/images/link.png", "AssetFile": "Temp/images/link.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "301"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"mRYlVl4c+XDmuCH3K+H8TdsWnKukPgJwHQQkw/UcOEY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mRYlVl4c+XDmuCH3K+H8TdsWnKukPgJwHQQkw/UcOEY="}]}, {"Route": "Temp/images/linkedin.00ii30lp16.png", "AssetFile": "Temp/images/linkedin.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1393"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oJ7DYlN6/uxlS4qJc2Px2mGRRnLSh65hf1lE+rNqLXY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "00ii30lp16"}, {"Name": "integrity", "Value": "sha256-oJ7DYlN6/uxlS4qJc2Px2mGRRnLSh65hf1lE+rNqLXY="}, {"Name": "label", "Value": "Temp/images/linkedin.png"}]}, {"Route": "Temp/images/linkedin.png", "AssetFile": "Temp/images/linkedin.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "1393"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oJ7DYlN6/uxlS4qJc2Px2mGRRnLSh65hf1lE+rNqLXY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oJ7DYlN6/uxlS4qJc2Px2mGRRnLSh65hf1lE+rNqLXY="}]}, {"Route": "Temp/images/location.lqopbyp49g.png", "AssetFile": "Temp/images/location.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "862"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"g3OvFJ6vlKro4fOqqhxNvcSprOS6MT2POYXDKCVza7c=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lqopbyp49g"}, {"Name": "integrity", "Value": "sha256-g3OvFJ6vlKro4fOqqhxNvcSprOS6MT2POYXDKCVza7c="}, {"Name": "label", "Value": "Temp/images/location.png"}]}, {"Route": "Temp/images/location.png", "AssetFile": "Temp/images/location.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "862"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"g3OvFJ6vlKro4fOqqhxNvcSprOS6MT2POYXDKCVza7c=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g3OvFJ6vlKro4fOqqhxNvcSprOS6MT2POYXDKCVza7c="}]}, {"Route": "Temp/images/logo.h3o0p1c2ho.png", "AssetFile": "Temp/images/logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2275"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"A+kBsGoPUbLKNJ+zC7TlhgDKWBPFPnJGvsHP2EDl6qc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h3o0p1c2ho"}, {"Name": "integrity", "Value": "sha256-A+kBsGoPUbLKNJ+zC7TlhgDKWBPFPnJGvsHP2EDl6qc="}, {"Name": "label", "Value": "Temp/images/logo.png"}]}, {"Route": "Temp/images/logo.png", "AssetFile": "Temp/images/logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "2275"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"A+kBsGoPUbLKNJ+zC7TlhgDKWBPFPnJGvsHP2EDl6qc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-A+kBsGoPUbLKNJ+zC7TlhgDKWBPFPnJGvsHP2EDl6qc="}]}, {"Route": "Temp/images/mail.j912x17548.png", "AssetFile": "Temp/images/mail.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "708"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"GXiBR6YS0QO3K7e2l3FIT63r6JAZ47KfFR4BepR/U+g=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j912x17548"}, {"Name": "integrity", "Value": "sha256-GXiBR6YS0QO3K7e2l3FIT63r6JAZ47KfFR4BepR/U+g="}, {"Name": "label", "Value": "Temp/images/mail.png"}]}, {"Route": "Temp/images/mail.png", "AssetFile": "Temp/images/mail.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "708"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"GXiBR6YS0QO3K7e2l3FIT63r6JAZ47KfFR4BepR/U+g=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GXiBR6YS0QO3K7e2l3FIT63r6JAZ47KfFR4BepR/U+g="}]}, {"Route": "Temp/images/menu.1arn9jpg60.png", "AssetFile": "Temp/images/menu.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "159"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"J7Grlj+a1TzpnQwvpShwFmrzAM9rp8NayPxWEDkxVU8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1arn9jpg60"}, {"Name": "integrity", "Value": "sha256-J7Grlj+a1TzpnQwvpShwFmrzAM9rp8NayPxWEDkxVU8="}, {"Name": "label", "Value": "Temp/images/menu.png"}]}, {"Route": "Temp/images/menu.png", "AssetFile": "Temp/images/menu.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "159"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"J7Grlj+a1TzpnQwvpShwFmrzAM9rp8NayPxWEDkxVU8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J7Grlj+a1TzpnQwvpShwFmrzAM9rp8NayPxWEDkxVU8="}]}, {"Route": "Temp/images/next-arrow.73bbo8vnii.png", "AssetFile": "Temp/images/next-arrow.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "230"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4/RIeQHegAthMVay8awBe9SYGm7nW+BSY158hB1Ce8o=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "73bbo8vnii"}, {"Name": "integrity", "Value": "sha256-4/RIeQHegAthMVay8awBe9SYGm7nW+BSY158hB1Ce8o="}, {"Name": "label", "Value": "Temp/images/next-arrow.png"}]}, {"Route": "Temp/images/next-arrow.png", "AssetFile": "Temp/images/next-arrow.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "230"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4/RIeQHegAthMVay8awBe9SYGm7nW+BSY158hB1Ce8o=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4/RIeQHegAthMVay8awBe9SYGm7nW+BSY158hB1Ce8o="}]}, {"Route": "Temp/images/next.72j0a5okp2.png", "AssetFile": "Temp/images/next.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "200"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"gaHiGr0DC/iQa8j/41iOIc0Fq0WuExgpvaaI7pwAxtM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "72j0a5okp2"}, {"Name": "integrity", "Value": "sha256-gaHiGr0DC/iQa8j/41iOIc0Fq0WuExgpvaaI7pwAxtM="}, {"Name": "label", "Value": "Temp/images/next.png"}]}, {"Route": "Temp/images/next.png", "AssetFile": "Temp/images/next.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "200"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"gaHiGr0DC/iQa8j/41iOIc0Fq0WuExgpvaaI7pwAxtM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gaHiGr0DC/iQa8j/41iOIc0Fq0WuExgpvaaI7pwAxtM="}]}, {"Route": "Temp/images/p1.aebq0cbn1v.jpg", "AssetFile": "Temp/images/p1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "55022"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"MtxcqFoaZm8YH8g/LADyItSlsT3ugSHQAFDAMecjwIM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aebq0cbn1v"}, {"Name": "integrity", "Value": "sha256-MtxcqFoaZm8YH8g/LADyItSlsT3ugSHQAFDAMecjwIM="}, {"Name": "label", "Value": "Temp/images/p1.jpg"}]}, {"Route": "Temp/images/p1.jpg", "AssetFile": "Temp/images/p1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "55022"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"MtxcqFoaZm8YH8g/LADyItSlsT3ugSHQAFDAMecjwIM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MtxcqFoaZm8YH8g/LADyItSlsT3ugSHQAFDAMecjwIM="}]}, {"Route": "Temp/images/p2.jpg", "AssetFile": "Temp/images/p2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "71162"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"HHO1hti+l3OwzZ88MHv+F1gbV12SzZL0C5WKSRQrMrs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HHO1hti+l3OwzZ88MHv+F1gbV12SzZL0C5WKSRQrMrs="}]}, {"Route": "Temp/images/p2.o75268m7b0.jpg", "AssetFile": "Temp/images/p2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "71162"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"HHO1hti+l3OwzZ88MHv+F1gbV12SzZL0C5WKSRQrMrs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o75268m7b0"}, {"Name": "integrity", "Value": "sha256-HHO1hti+l3OwzZ88MHv+F1gbV12SzZL0C5WKSRQrMrs="}, {"Name": "label", "Value": "Temp/images/p2.jpg"}]}, {"Route": "Temp/images/p3.8xa92ve2bk.jpg", "AssetFile": "Temp/images/p3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "40364"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"dOKBqTUUfdvWuR2bkfwjpQIw0RVL4DE9XRuDLm4+YOo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8xa92ve2bk"}, {"Name": "integrity", "Value": "sha256-dOKBqTUUfdvWuR2bkfwjpQIw0RVL4DE9XRuDLm4+YOo="}, {"Name": "label", "Value": "Temp/images/p3.jpg"}]}, {"Route": "Temp/images/p3.jpg", "AssetFile": "Temp/images/p3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "40364"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"dOKBqTUUfdvWuR2bkfwjpQIw0RVL4DE9XRuDLm4+YOo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dOKBqTUUfdvWuR2bkfwjpQIw0RVL4DE9XRuDLm4+YOo="}]}, {"Route": "Temp/images/p4.jpg", "AssetFile": "Temp/images/p4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "34631"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"4/r1a0tFlxIp3v+ovDDG11ghU9umPqJIx8pyCsKG6l0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4/r1a0tFlxIp3v+ovDDG11ghU9umPqJIx8pyCsKG6l0="}]}, {"Route": "Temp/images/p4.vhajvs1bkd.jpg", "AssetFile": "Temp/images/p4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "34631"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"4/r1a0tFlxIp3v+ovDDG11ghU9umPqJIx8pyCsKG6l0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vhajvs1bkd"}, {"Name": "integrity", "Value": "sha256-4/r1a0tFlxIp3v+ovDDG11ghU9umPqJIx8pyCsKG6l0="}, {"Name": "label", "Value": "Temp/images/p4.jpg"}]}, {"Route": "Temp/images/prev-arrow.bpcsm5v197.png", "AssetFile": "Temp/images/prev-arrow.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "224"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"T2mjv8z0qshBxlgURy/rlFX2FTUIP9azuf98i+ItE30=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpcsm5v197"}, {"Name": "integrity", "Value": "sha256-T2mjv8z0qshBxlgURy/rlFX2FTUIP9azuf98i+ItE30="}, {"Name": "label", "Value": "Temp/images/prev-arrow.png"}]}, {"Route": "Temp/images/prev-arrow.png", "AssetFile": "Temp/images/prev-arrow.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "224"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"T2mjv8z0qshBxlgURy/rlFX2FTUIP9azuf98i+ItE30=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T2mjv8z0qshBxlgURy/rlFX2FTUIP9azuf98i+ItE30="}]}, {"Route": "Temp/images/prev.png", "AssetFile": "Temp/images/prev.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "207"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"aZf4/GY/K/jM1yAN6KJKQd8K7RojoW7UebHcGAMZyps=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aZf4/GY/K/jM1yAN6KJKQd8K7RojoW7UebHcGAMZyps="}]}, {"Route": "Temp/images/prev.vadpt3nrn7.png", "AssetFile": "Temp/images/prev.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "207"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"aZf4/GY/K/jM1yAN6KJKQd8K7RojoW7UebHcGAMZyps=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vadpt3nrn7"}, {"Name": "integrity", "Value": "sha256-aZf4/GY/K/jM1yAN6KJKQd8K7RojoW7UebHcGAMZyps="}, {"Name": "label", "Value": "Temp/images/prev.png"}]}, {"Route": "Temp/images/quote.d6j09uiz4y.png", "AssetFile": "Temp/images/quote.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "592"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"692ba5wfOILEkoNvauSUq3gCsTXpeTdJzfSP/ikagpo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d6j09uiz4y"}, {"Name": "integrity", "Value": "sha256-692ba5wfOILEkoNvauSUq3gCsTXpeTdJzfSP/ikagpo="}, {"Name": "label", "Value": "Temp/images/quote.png"}]}, {"Route": "Temp/images/quote.png", "AssetFile": "Temp/images/quote.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "592"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"692ba5wfOILEkoNvauSUq3gCsTXpeTdJzfSP/ikagpo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-692ba5wfOILEkoNvauSUq3gCsTXpeTdJzfSP/ikagpo="}]}, {"Route": "Temp/images/search-icon.i2euaea8x3.png", "AssetFile": "Temp/images/search-icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "363"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Rns/+v7e+r/V7OTT4aPiAaJuUAnMx65qgbMSMkcLI7I=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "i2euaea8x3"}, {"Name": "integrity", "Value": "sha256-Rns/+v7e+r/V7OTT4aPiAaJuUAnMx65qgbMSMkcLI7I="}, {"Name": "label", "Value": "Temp/images/search-icon.png"}]}, {"Route": "Temp/images/search-icon.png", "AssetFile": "Temp/images/search-icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "363"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Rns/+v7e+r/V7OTT4aPiAaJuUAnMx65qgbMSMkcLI7I=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Rns/+v7e+r/V7OTT4aPiAaJuUAnMx65qgbMSMkcLI7I="}]}, {"Route": "Temp/images/search.mdioaqld5g.png", "AssetFile": "Temp/images/search.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "308"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"9gp/AnUoUP/j4M/efHX0BrOshaT/63m+qFMLpiqeSlk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mdioaqld5g"}, {"Name": "integrity", "Value": "sha256-9gp/AnUoUP/j4M/efHX0BrOshaT/63m+qFMLpiqeSlk="}, {"Name": "label", "Value": "Temp/images/search.png"}]}, {"Route": "Temp/images/search.png", "AssetFile": "Temp/images/search.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "308"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"9gp/AnUoUP/j4M/efHX0BrOshaT/63m+qFMLpiqeSlk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9gp/AnUoUP/j4M/efHX0BrOshaT/63m+qFMLpiqeSlk="}]}, {"Route": "Temp/images/service-bg.j1yzkmm699.jpg", "AssetFile": "Temp/images/service-bg.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "88630"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"6dzNK9EreSyI2fpou4OX932RRVekiQZrEJcTxeng3N0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j1yzkmm699"}, {"Name": "integrity", "Value": "sha256-6dzNK9EreSyI2fpou4OX932RRVekiQZrEJcTxeng3N0="}, {"Name": "label", "Value": "Temp/images/service-bg.jpg"}]}, {"Route": "Temp/images/service-bg.jpg", "AssetFile": "Temp/images/service-bg.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "88630"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"6dzNK9EreSyI2fpou4OX932RRVekiQZrEJcTxeng3N0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6dzNK9EreSyI2fpou4OX932RRVekiQZrEJcTxeng3N0="}]}, {"Route": "Temp/images/service-img.jpg", "AssetFile": "Temp/images/service-img.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "51930"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"0xvEX0ucpZzV3HBH8iOV6+3IHRX18ZtP3DY/zvE3cRw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0xvEX0ucpZzV3HBH8iOV6+3IHRX18ZtP3DY/zvE3cRw="}]}, {"Route": "Temp/images/service-img.xr408hq10h.jpg", "AssetFile": "Temp/images/service-img.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51930"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"0xvEX0ucpZzV3HBH8iOV6+3IHRX18ZtP3DY/zvE3cRw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xr408hq10h"}, {"Name": "integrity", "Value": "sha256-0xvEX0ucpZzV3HBH8iOV6+3IHRX18ZtP3DY/zvE3cRw="}, {"Name": "label", "Value": "Temp/images/service-img.jpg"}]}, {"Route": "Temp/images/slider-bg.209xa3yu7k.jpg", "AssetFile": "Temp/images/slider-bg.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "75243"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Ln4lfbBhNF2BI2NEM7UV8aJxplvipFIBkQbWJn1i8Gk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "209xa3yu7k"}, {"Name": "integrity", "Value": "sha256-Ln4lfbBhNF2BI2NEM7UV8aJxplvipFIBkQbWJn1i8Gk="}, {"Name": "label", "Value": "Temp/images/slider-bg.jpg"}]}, {"Route": "Temp/images/slider-bg.jpg", "AssetFile": "Temp/images/slider-bg.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "75243"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Ln4lfbBhNF2BI2NEM7UV8aJxplvipFIBkQbWJn1i8Gk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ln4lfbBhNF2BI2NEM7UV8aJxplvipFIBkQbWJn1i8Gk="}]}, {"Route": "Temp/images/slider-img.jpg", "AssetFile": "Temp/images/slider-img.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "78645"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"WWZj+ASwFay7FwnyYGKXFLpyRCP5L/Zbmz9qFBI5x+M=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WWZj+ASwFay7FwnyYGKXFLpyRCP5L/Zbmz9qFBI5x+M="}]}, {"Route": "Temp/images/slider-img.vupb28i3ic.jpg", "AssetFile": "Temp/images/slider-img.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "78645"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"WWZj+ASwFay7FwnyYGKXFLpyRCP5L/Zbmz9qFBI5x+M=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vupb28i3ic"}, {"Name": "integrity", "Value": "sha256-WWZj+ASwFay7FwnyYGKXFLpyRCP5L/Zbmz9qFBI5x+M="}, {"Name": "label", "Value": "Temp/images/slider-img.jpg"}]}, {"Route": "Temp/images/twitter.png", "AssetFile": "Temp/images/twitter.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "1489"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Xtvaz5X+v2TqstoQOn02WYuCufrtdX/z4iv0nXfoi5M=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xtvaz5X+v2TqstoQOn02WYuCufrtdX/z4iv0nXfoi5M="}]}, {"Route": "Temp/images/twitter.uxinhxhypx.png", "AssetFile": "Temp/images/twitter.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1489"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Xtvaz5X+v2TqstoQOn02WYuCufrtdX/z4iv0nXfoi5M=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uxinhxhypx"}, {"Name": "integrity", "Value": "sha256-Xtvaz5X+v2TqstoQOn02WYuCufrtdX/z4iv0nXfoi5M="}, {"Name": "label", "Value": "Temp/images/twitter.png"}]}, {"Route": "Temp/images/youtube.6abc36v3p4.png", "AssetFile": "Temp/images/youtube.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1450"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4tCjz6tS6jOWdgbOTIoHThXmQB2FhGmaCjgJBgIG4zs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6abc36v3p4"}, {"Name": "integrity", "Value": "sha256-4tCjz6tS6jOWdgbOTIoHThXmQB2FhGmaCjgJBgIG4zs="}, {"Name": "label", "Value": "Temp/images/youtube.png"}]}, {"Route": "Temp/images/youtube.png", "AssetFile": "Temp/images/youtube.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "1450"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4tCjz6tS6jOWdgbOTIoHThXmQB2FhGmaCjgJBgIG4zs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4tCjz6tS6jOWdgbOTIoHThXmQB2FhGmaCjgJBgIG4zs="}]}, {"Route": "Temp/js/bootstrap.js", "AssetFile": "Temp/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "136796"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VwfQsO5UEA7u1CKC7q+lxAt5wfTA2uBa+5nQ1OIGSEU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VwfQsO5UEA7u1CKC7q+lxAt5wfTA2uBa+5nQ1OIGSEU="}]}, {"Route": "Temp/js/bootstrap.pcy7vwh15u.js", "AssetFile": "Temp/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "136796"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VwfQsO5UEA7u1CKC7q+lxAt5wfTA2uBa+5nQ1OIGSEU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pcy7vwh15u"}, {"Name": "integrity", "Value": "sha256-VwfQsO5UEA7u1CKC7q+lxAt5wfTA2uBa+5nQ1OIGSEU="}, {"Name": "label", "Value": "Temp/js/bootstrap.js"}]}, {"Route": "Temp/js/custom.js", "AssetFile": "Temp/js/custom.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "551"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XJ2N3+vtTYAXA9KnT7rLtA4eIL3DG3naWOZxnHhcjGc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XJ2N3+vtTYAXA9KnT7rLtA4eIL3DG3naWOZxnHhcjGc="}]}, {"Route": "Temp/js/custom.wvm2d87knn.js", "AssetFile": "Temp/js/custom.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "551"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XJ2N3+vtTYAXA9KnT7rLtA4eIL3DG3naWOZxnHhcjGc=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wvm2d87knn"}, {"Name": "integrity", "Value": "sha256-XJ2N3+vtTYAXA9KnT7rLtA4eIL3DG3naWOZxnHhcjGc="}, {"Name": "label", "Value": "Temp/js/custom.js"}]}, {"Route": "Temp/js/jquery-3.4.1.min.d6rpaas0a0.js", "AssetFile": "Temp/js/jquery-3.4.1.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "88145"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KzgTY92gSfLUmlkDeyKLyGXVH/uXfI9cNUfVwo3kjjo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d6rpaas0a0"}, {"Name": "integrity", "Value": "sha256-KzgTY92gSfLUmlkDeyKLyGXVH/uXfI9cNUfVwo3kjjo="}, {"Name": "label", "Value": "Temp/js/jquery-3.4.1.min.js"}]}, {"Route": "Temp/js/jquery-3.4.1.min.js", "AssetFile": "Temp/js/jquery-3.4.1.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "88145"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KzgTY92gSfLUmlkDeyKLyGXVH/uXfI9cNUfVwo3kjjo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 04 Mar 2025 12:54:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KzgTY92gSfLUmlkDeyKLyGXVH/uXfI9cNUfVwo3kjjo="}]}, {"Route": "css/AURORA_README.batbu01mml.md", "AssetFile": "css/AURORA_README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4339"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"/Sh0ggP9Zc+rMJkifRRE4J/+3VPz1LvamRbVVx+e/LE=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 14:30:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "batbu01mml"}, {"Name": "integrity", "Value": "sha256-/Sh0ggP9Zc+rMJkifRRE4J/+3VPz1LvamRbVVx+e/LE="}, {"Name": "label", "Value": "css/AURORA_README.md"}]}, {"Route": "css/AURORA_README.md", "AssetFile": "css/AURORA_README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4339"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"/Sh0ggP9Zc+rMJkifRRE4J/+3VPz1LvamRbVVx+e/LE=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 14:30:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/Sh0ggP9Zc+rMJkifRRE4J/+3VPz1LvamRbVVx+e/LE="}]}, {"Route": "css/aurora.css", "AssetFile": "css/aurora.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9838"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YihNc7Bh2dvD8PY7ihDDqqOAdgCsN2SXWAwWTLg5zXk=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 14:28:01 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YihNc7Bh2dvD8PY7ihDDqqOAdgCsN2SXWAwWTLg5zXk="}]}, {"Route": "css/aurora.ijifg3561v.css", "AssetFile": "css/aurora.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9838"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YihNc7Bh2dvD8PY7ihDDqqOAdgCsN2SXWAwWTLg5zXk=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 14:28:01 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ijifg3561v"}, {"Name": "integrity", "Value": "sha256-YihNc7Bh2dvD8PY7ihDDqqOAdgCsN2SXWAwWTLg5zXk="}, {"Name": "label", "Value": "css/aurora.css"}]}, {"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14819"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Z7VWTHKXQP2uYHy29p1Vmlj/HrHDysdq0iE6n9rrM9w=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 13:55:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Z7VWTHKXQP2uYHy29p1Vmlj/HrHDysdq0iE6n9rrM9w="}]}, {"Route": "css/site.hu1tsk7ntp.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14819"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Z7VWTHKXQP2uYHy29p1Vmlj/HrHDysdq0iE6n9rrM9w=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 13:55:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hu1tsk7ntp"}, {"Name": "integrity", "Value": "sha256-Z7VWTHKXQP2uYHy29p1Vmlj/HrHDysdq0iE6n9rrM9w="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "images/1.jpg", "AssetFile": "images/1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "25128"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"D+sFXn7W56wkt1ksyMSs/OPU+zT6yXDdy+5IszklMVA=\""}, {"Name": "Last-Modified", "Value": "Thu, 27 Feb 2025 12:26:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-D+sFXn7W56wkt1ksyMSs/OPU+zT6yXDdy+5IszklMVA="}]}, {"Route": "images/1.rh9wjc9nxq.jpg", "AssetFile": "images/1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "25128"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"D+sFXn7W56wkt1ksyMSs/OPU+zT6yXDdy+5IszklMVA=\""}, {"Name": "Last-Modified", "Value": "Thu, 27 Feb 2025 12:26:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rh9wjc9nxq"}, {"Name": "integrity", "Value": "sha256-D+sFXn7W56wkt1ksyMSs/OPU+zT6yXDdy+5IszklMVA="}, {"Name": "label", "Value": "images/1.jpg"}]}, {"Route": "images/2.iifgjilap6.png", "AssetFile": "images/2.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2246994"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"iv39PXpDubtyaCuNYYQxG9qOyCwL0ewziRhw6q76LCs=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Oct 2024 11:17:14 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iifgjilap6"}, {"Name": "integrity", "Value": "sha256-iv39PXpDubtyaCuNYYQxG9qOyCwL0ewziRhw6q76LCs="}, {"Name": "label", "Value": "images/2.png"}]}, {"Route": "images/2.png", "AssetFile": "images/2.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "2246994"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"iv39PXpDubtyaCuNYYQxG9qOyCwL0ewziRhw6q76LCs=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Oct 2024 11:17:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iv39PXpDubtyaCuNYYQxG9qOyCwL0ewziRhw6q76LCs="}]}, {"Route": "images/avatars/58947ca9-df64-49b6-93b1-020619a5e9d6.7622xmlyzf.jpg", "AssetFile": "images/avatars/58947ca9-df64-49b6-93b1-020619a5e9d6.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "77830"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"gd/2IR8rr4qDvC78xFE838iPHRz3RREs3cXZ1XuRkyg=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 12:22:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7622xmlyzf"}, {"Name": "integrity", "Value": "sha256-gd/2IR8rr4qDvC78xFE838iPHRz3RREs3cXZ1XuRkyg="}, {"Name": "label", "Value": "images/avatars/58947ca9-df64-49b6-93b1-020619a5e9d6.jpg"}]}, {"Route": "images/avatars/58947ca9-df64-49b6-93b1-020619a5e9d6.jpg", "AssetFile": "images/avatars/58947ca9-df64-49b6-93b1-020619a5e9d6.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "77830"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"gd/2IR8rr4qDvC78xFE838iPHRz3RREs3cXZ1XuRkyg=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 12:22:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gd/2IR8rr4qDvC78xFE838iPHRz3RREs3cXZ1XuRkyg="}]}, {"Route": "images/avatars/7350196f-ef39-48a6-a083-d8ac77e23c8d.jpg", "AssetFile": "images/avatars/7350196f-ef39-48a6-a083-d8ac77e23c8d.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "250000"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"LkOcNoKxINBnccJHmwu+PBLFqwg2T4N2Lfycqelkb8o=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 12:01:32 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LkOcNoKxINBnccJHmwu+PBLFqwg2T4N2Lfycqelkb8o="}]}, {"Route": "images/avatars/7350196f-ef39-48a6-a083-d8ac77e23c8d.yz6zhx6c71.jpg", "AssetFile": "images/avatars/7350196f-ef39-48a6-a083-d8ac77e23c8d.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "250000"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"LkOcNoKxINBnccJHmwu+PBLFqwg2T4N2Lfycqelkb8o=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 12:01:32 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yz6zhx6c71"}, {"Name": "integrity", "Value": "sha256-LkOcNoKxINBnccJHmwu+PBLFqwg2T4N2Lfycqelkb8o="}, {"Name": "label", "Value": "images/avatars/7350196f-ef39-48a6-a083-d8ac77e23c8d.jpg"}]}, {"Route": "images/avatars/88013c2d-3ff5-4f70-8f98-ab1507c7e9e0.jpg", "AssetFile": "images/avatars/88013c2d-3ff5-4f70-8f98-ab1507c7e9e0.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "80627"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"UE3KF/b+HliyBQPc/XTUkKSYDB9NybrZuJuDvlvxyvE=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 12:21:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UE3KF/b+HliyBQPc/XTUkKSYDB9NybrZuJuDvlvxyvE="}]}, {"Route": "images/avatars/88013c2d-3ff5-4f70-8f98-ab1507c7e9e0.wcqhfx4lmn.jpg", "AssetFile": "images/avatars/88013c2d-3ff5-4f70-8f98-ab1507c7e9e0.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "80627"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"UE3KF/b+HliyBQPc/XTUkKSYDB9NybrZuJuDvlvxyvE=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 12:21:52 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wcqhfx4lmn"}, {"Name": "integrity", "Value": "sha256-UE3KF/b+HliyBQPc/XTUkKSYDB9NybrZuJuDvlvxyvE="}, {"Name": "label", "Value": "images/avatars/88013c2d-3ff5-4f70-8f98-ab1507c7e9e0.jpg"}]}, {"Route": "images/avatars/90e84407-47d5-4132-a384-d8cdd78b0132.6bbybugm8u.png", "AssetFile": "images/avatars/90e84407-47d5-4132-a384-d8cdd78b0132.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "704230"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"whDc+V3inDE2Iz4JcLnxrbKC04wt9AiLR3NlFl0DslQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 12:01:11 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6bbybugm8u"}, {"Name": "integrity", "Value": "sha256-whDc+V3inDE2Iz4JcLnxrbKC04wt9AiLR3NlFl0DslQ="}, {"Name": "label", "Value": "images/avatars/90e84407-47d5-4132-a384-d8cdd78b0132.png"}]}, {"Route": "images/avatars/90e84407-47d5-4132-a384-d8cdd78b0132.png", "AssetFile": "images/avatars/90e84407-47d5-4132-a384-d8cdd78b0132.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "704230"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"whDc+V3inDE2Iz4JcLnxrbKC04wt9AiLR3NlFl0DslQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 12:01:11 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-whDc+V3inDE2Iz4JcLnxrbKC04wt9AiLR3NlFl0DslQ="}]}, {"Route": "images/placeholder.2p43rz2bf6.svg", "AssetFile": "images/placeholder.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "280"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"hoF8mHWARpBC51V9yYbfZho1gV3pkaNecgiQO2gHoVM=\""}, {"Name": "Last-Modified", "Value": "Tue, 03 Jun 2025 04:18:13 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2p43rz2bf6"}, {"Name": "integrity", "Value": "sha256-hoF8mHWARpBC51V9yYbfZho1gV3pkaNecgiQO2gHoVM="}, {"Name": "label", "Value": "images/placeholder.svg"}]}, {"Route": "images/placeholder.svg", "AssetFile": "images/placeholder.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "280"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"hoF8mHWARpBC51V9yYbfZho1gV3pkaNecgiQO2gHoVM=\""}, {"Name": "Last-Modified", "Value": "Tue, 03 Jun 2025 04:18:13 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hoF8mHWARpBC51V9yYbfZho1gV3pkaNecgiQO2gHoVM="}]}, {"Route": "images/products/42c8b158-e5b5-4bb1-8ce0-14a495c1b520.2mjv40zzcc.webp", "AssetFile": "images/products/42c8b158-e5b5-4bb1-8ce0-14a495c1b520.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "57534"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"9hZVati44ZGl8RSoVd42B1PrjPp350IjfV1W4wMLIXs=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 14:48:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2mjv40zzcc"}, {"Name": "integrity", "Value": "sha256-9hZVati44ZGl8RSoVd42B1PrjPp350IjfV1W4wMLIXs="}, {"Name": "label", "Value": "images/products/42c8b158-e5b5-4bb1-8ce0-14a495c1b520.webp"}]}, {"Route": "images/products/42c8b158-e5b5-4bb1-8ce0-14a495c1b520.webp", "AssetFile": "images/products/42c8b158-e5b5-4bb1-8ce0-14a495c1b520.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "57534"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"9hZVati44ZGl8RSoVd42B1PrjPp350IjfV1W4wMLIXs=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 14:48:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9hZVati44ZGl8RSoVd42B1PrjPp350IjfV1W4wMLIXs="}]}, {"Route": "images/products/b317d67f-cb56-4146-a0a0-22531bf5852d.5t61ech6wa.jpg", "AssetFile": "images/products/b317d67f-cb56-4146-a0a0-22531bf5852d.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "830206"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"ueZEC0qp/GZjK0Yw9YtHmmk4dvRTIb6A0z9mc3cTFD0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 15:23:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5t61ech6wa"}, {"Name": "integrity", "Value": "sha256-ueZEC0qp/GZjK0Yw9YtHmmk4dvRTIb6A0z9mc3cTFD0="}, {"Name": "label", "Value": "images/products/b317d67f-cb56-4146-a0a0-22531bf5852d.jpg"}]}, {"Route": "images/products/b317d67f-cb56-4146-a0a0-22531bf5852d.jpg", "AssetFile": "images/products/b317d67f-cb56-4146-a0a0-22531bf5852d.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "830206"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"ueZEC0qp/GZjK0Yw9YtHmmk4dvRTIb6A0z9mc3cTFD0=\""}, {"Name": "Last-Modified", "Value": "Thu, 05 Jun 2025 15:23:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ueZEC0qp/GZjK0Yw9YtHmmk4dvRTIb6A0z9mc3cTFD0="}]}, {"Route": "images/products/f2ad78ce-55b7-4d81-8e83-eb3dde20c9b2.2mjv40zzcc.webp", "AssetFile": "images/products/f2ad78ce-55b7-4d81-8e83-eb3dde20c9b2.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "57534"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"9hZVati44ZGl8RSoVd42B1PrjPp350IjfV1W4wMLIXs=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 14:36:34 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2mjv40zzcc"}, {"Name": "integrity", "Value": "sha256-9hZVati44ZGl8RSoVd42B1PrjPp350IjfV1W4wMLIXs="}, {"Name": "label", "Value": "images/products/f2ad78ce-55b7-4d81-8e83-eb3dde20c9b2.webp"}]}, {"Route": "images/products/f2ad78ce-55b7-4d81-8e83-eb3dde20c9b2.webp", "AssetFile": "images/products/f2ad78ce-55b7-4d81-8e83-eb3dde20c9b2.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "57534"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"9hZVati44ZGl8RSoVd42B1PrjPp350IjfV1W4wMLIXs=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 14:36:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9hZVati44ZGl8RSoVd42B1PrjPp350IjfV1W4wMLIXs="}]}, {"Route": "js/site.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "231"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo="}]}, {"Route": "js/site.xtxxf3hu2r.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "231"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xtxxf3hu2r"}, {"Name": "integrity", "Value": "sha256-hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo="}, {"Name": "label", "Value": "js/site.js"}]}, {"Route": "lib/bootstrap/LICENSE", "AssetFile": "lib/bootstrap/LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}]}, {"Route": "lib/bootstrap/LICENSE.81b7ukuj9c", "AssetFile": "lib/bootstrap/LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "81b7ukuj9c"}, {"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}, {"Name": "label", "Value": "lib/bootstrap/LICENSE"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.agp80tu62r.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "70538"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "agp80tu62r"}, {"Name": "integrity", "Value": "sha256-JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "70538"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "196535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.st1cbwfwo5.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "196535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "st1cbwfwo5"}, {"Name": "integrity", "Value": "sha256-QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51319"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.5vj65cig9w.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "117439"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5vj65cig9w"}, {"Name": "integrity", "Value": "sha256-72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "117439"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.unj9p35syc.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51319"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "unj9p35syc"}, {"Name": "integrity", "Value": "sha256-ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "70612"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.2q4vfeazbq.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "196539"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2q4vfeazbq"}, {"Name": "integrity", "Value": "sha256-qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "196539"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "117516"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.o371a8zbv2.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "117516"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o371a8zbv2"}, {"Name": "integrity", "Value": "sha256-NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.n1oizzvkh6.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51394"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n1oizzvkh6"}, {"Name": "integrity", "Value": "sha256-O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.q2ku51ktnl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "70612"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q2ku51ktnl"}, {"Name": "integrity", "Value": "sha256-3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.7na4sro3qu.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5850"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7na4sro3qu"}, {"Name": "integrity", "Value": "sha256-4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5850"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.jeal3x0ldm.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "105138"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jeal3x0ldm"}, {"Name": "integrity", "Value": "sha256-FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "105138"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4646"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "35330"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.okkk44j0xs.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "35330"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "okkk44j0xs"}, {"Name": "integrity", "Value": "sha256-2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.f8imaxxbri.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4646"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f8imaxxbri"}, {"Name": "integrity", "Value": "sha256-z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.0wve5yxp74.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5827"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0wve5yxp74"}, {"Name": "integrity", "Value": "sha256-8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5827"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.cwzlr5n8x4.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "105151"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cwzlr5n8x4"}, {"Name": "integrity", "Value": "sha256-/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "105151"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4718"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "41570"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.wmug9u23qg.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "41570"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wmug9u23qg"}, {"Name": "integrity", "Value": "sha256-GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.npxfuf8dg6.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4718"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "npxfuf8dg6"}, {"Name": "integrity", "Value": "sha256-a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "71584"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.j75batdsum.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "192271"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j75<PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "192271"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.16095smhkz.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "53479"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "16095smhkz"}, {"Name": "integrity", "Value": "sha256-5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53479"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "111875"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.vy0bq9ydhf.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "111875"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vy0bq9ydhf"}, {"Name": "integrity", "Value": "sha256-p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.b4skse8du6.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "71451"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b4skse8du6"}, {"Name": "integrity", "Value": "sha256-peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "71451"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.ab1c3rmv7g.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "192214"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ab1c3rmv7g"}, {"Name": "integrity", "Value": "sha256-puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "192214"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53407"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.56d2bn4wt9.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "111710"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "56d2bn4wt9"}, {"Name": "integrity", "Value": "sha256-02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "111710"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.u3xrusw2ol.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "53407"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u3xrusw2ol"}, {"Name": "integrity", "Value": "sha256-Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.tey0rigmnh.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "71584"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tey0rigmnh"}, {"Name": "integrity", "Value": "sha256-NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "204136"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.73kdqttayv.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "536547"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "73kdqttayv"}, {"Name": "integrity", "Value": "sha256-DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "536547"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.mpyigms19s.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "204136"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mpyigms19s"}, {"Name": "integrity", "Value": "sha256-xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "203803"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.4gxs3k148c.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "536461"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4gxs3k148c"}, {"Name": "integrity", "Value": "sha256-VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "536461"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.9b9oa1qrmt.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "162825"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9b9oa1qrmt"}, {"Name": "integrity", "Value": "sha256-22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162825"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.fctod5rc9n.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "661035"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fctod5rc9n"}, {"Name": "integrity", "Value": "sha256-j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "661035"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.ve6x09088i.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "203803"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ve6x09088i"}, {"Name": "integrity", "Value": "sha256-SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "208492"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.kbynt5jhd9.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "425643"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kbynt5jhd9"}, {"Name": "integrity", "Value": "sha256-gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "425643"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.l2av4jpuoj.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "208492"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l2av4jpuoj"}, {"Name": "integrity", "Value": "sha256-vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.25iw1kog22.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "78468"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "25iw1kog22"}, {"Name": "integrity", "Value": "sha256-KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "78468"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.c2nslu3uf3.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "327261"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2nslu3uf3"}, {"Name": "integrity", "Value": "sha256-xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "327261"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "139019"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.2lgwfvgpvi.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "288320"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2lgwfvgpvi"}, {"Name": "integrity", "Value": "sha256-CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "288320"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.m39kt2b5c9.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "139019"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m39kt2b5c9"}, {"Name": "integrity", "Value": "sha256-EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "72016"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "222508"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.wsezl0heh6.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "222508"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wsezl0heh6"}, {"Name": "integrity", "Value": "sha256-sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.um2aeqy4ik.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "72016"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "um2aeqy4ik"}, {"Name": "integrity", "Value": "sha256-Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "148168"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.6ukhryfubh.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "289522"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6<PERSON><PERSON><PERSON><PERSON>bh"}, {"Name": "integrity", "Value": "sha256-Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "289522"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "59511"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "217145"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.u33ctipx7g.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "217145"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u33ctipx7g"}, {"Name": "integrity", "Value": "sha256-ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.zwph15dxgs.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "59511"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zwph15dxgs"}, {"Name": "integrity", "Value": "sha256-c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.o4kw7cc6tf.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "148168"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o4kw7cc6tf"}, {"Name": "integrity", "Value": "sha256-6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.356vix0kms.txt", "AssetFile": "lib/jquery-validation-unobtrusive/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "356vix0kms"}, {"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt"}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "lib/jquery-validation-unobtrusive/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.47otxtyo56.js", "AssetFile": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47otxtyo56"}, {"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetFile": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.4v8eqarkd7.js", "AssetFile": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v8eqarkd7"}, {"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}]}, {"Route": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetFile": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation/LICENSE.md", "AssetFile": "lib/jquery-validation/LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md", "AssetFile": "lib/jquery-validation/LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md"}]}, {"Route": "lib/jquery-validation/dist/additional-methods.ay5nd8zt9x.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "52977"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ay5nd8zt9x"}, {"Name": "integrity", "Value": "sha256-4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E="}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js"}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "52977"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.9oaff4kq20.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9oaff4kq20"}, {"Name": "integrity", "Value": "sha256-N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc="}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js"}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.b7iojwaux1.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b7iojwaux1"}, {"Name": "integrity", "Value": "sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js"}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.pzqfkb6aqo.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pzqfkb6aqo"}, {"Name": "integrity", "Value": "sha256-m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw="}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js"}]}, {"Route": "lib/jquery/LICENSE.mlv21k5csn.txt", "AssetFile": "lib/jquery/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mlv21k5csn"}, {"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt"}]}, {"Route": "lib/jquery/LICENSE.txt", "AssetFile": "lib/jquery/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "lib/jquery/dist/jquery.fwhahm2icz.js", "AssetFile": "lib/jquery/dist/jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "288580"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fwhahm2icz"}, {"Name": "integrity", "Value": "sha256-H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js"}]}, {"Route": "lib/jquery/dist/jquery.js", "AssetFile": "lib/jquery/dist/jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "288580"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk="}]}, {"Route": "lib/jquery/dist/jquery.min.5pze98is44.map", "AssetFile": "lib/jquery/dist/jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "137972"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5pze98is44"}, {"Name": "integrity", "Value": "sha256-OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map"}]}, {"Route": "lib/jquery/dist/jquery.min.dd6z7egasc.js", "AssetFile": "lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "89501"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dd6z7egasc"}, {"Name": "integrity", "Value": "sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "89501"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4="}]}, {"Route": "lib/jquery/dist/jquery.min.map", "AssetFile": "lib/jquery/dist/jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "137972"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=\""}, {"Name": "Last-Modified", "Value": "Fri, 07 Mar 2025 05:51:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I="}]}]}