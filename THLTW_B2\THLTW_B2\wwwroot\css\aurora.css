/* Aurora Design System CSS */
/* Modern, elegant design with gradient backgrounds and glass morphism effects */

:root {
    /* Aurora Color Palette */
    --aurora-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --aurora-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --aurora-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --aurora-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --aurora-danger: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    --aurora-info: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    
    /* Neutral Colors */
    --aurora-dark: #1a1d29;
    --aurora-darker: #151821;
    --aurora-light: #f8fafc;
    --aurora-text: #64748b;
    --aurora-text-dark: #334155;
    --aurora-text-light: #94a3b8;
    
    /* Spacing */
    --aurora-spacing-xs: 0.25rem;
    --aurora-spacing-sm: 0.5rem;
    --aurora-spacing-md: 1rem;
    --aurora-spacing-lg: 1.5rem;
    --aurora-spacing-xl: 2rem;
    --aurora-spacing-2xl: 3rem;
    
    /* Border Radius */
    --aurora-radius-sm: 8px;
    --aurora-radius-md: 12px;
    --aurora-radius-lg: 16px;
    --aurora-radius-xl: 20px;
    --aurora-radius-2xl: 24px;
    
    /* Shadows */
    --aurora-shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.05);
    --aurora-shadow-md: 0 4px 16px rgba(0, 0, 0, 0.1);
    --aurora-shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.15);
    --aurora-shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.2);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--aurora-text-dark);
    background: var(--aurora-light);
}

/* Aurora Glass Card Component */
.aurora-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--aurora-radius-xl);
    box-shadow: var(--aurora-shadow-lg);
    transition: all 0.3s ease;
}

.aurora-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--aurora-shadow-xl);
}

.aurora-card-header {
    padding: var(--aurora-spacing-xl);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.aurora-card-body {
    padding: var(--aurora-spacing-xl);
}

.aurora-card-footer {
    padding: var(--aurora-spacing-xl);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Aurora Button Components */
.aurora-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--aurora-spacing-sm);
    padding: var(--aurora-spacing-md) var(--aurora-spacing-lg);
    border: none;
    border-radius: var(--aurora-radius-md);
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.aurora-btn-primary {
    background: var(--aurora-primary);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.aurora-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.aurora-btn-secondary {
    background: var(--aurora-secondary);
    color: white;
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
}

.aurora-btn-success {
    background: var(--aurora-success);
    color: white;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.aurora-btn-warning {
    background: var(--aurora-warning);
    color: white;
    box-shadow: 0 4px 15px rgba(67, 233, 123, 0.3);
}

.aurora-btn-danger {
    background: var(--aurora-danger);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.aurora-btn-outline {
    background: transparent;
    color: var(--aurora-text-dark);
    border: 2px solid rgba(102, 126, 234, 0.2);
}

.aurora-btn-outline:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
    color: #667eea;
}

/* Aurora Form Components */
.aurora-form-group {
    margin-bottom: var(--aurora-spacing-lg);
}

.aurora-form-label {
    display: block;
    margin-bottom: var(--aurora-spacing-sm);
    font-weight: 600;
    color: var(--aurora-text-dark);
}

.aurora-form-input {
    width: 100%;
    padding: var(--aurora-spacing-md) var(--aurora-spacing-lg);
    border: 2px solid rgba(102, 126, 234, 0.1);
    border-radius: var(--aurora-radius-md);
    background: rgba(248, 250, 252, 0.8);
    font-size: 1rem;
    transition: all 0.3s ease;
    outline: none;
}

.aurora-form-input:focus {
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
}

.aurora-form-select {
    width: 100%;
    padding: var(--aurora-spacing-md) var(--aurora-spacing-lg);
    border: 2px solid rgba(102, 126, 234, 0.1);
    border-radius: var(--aurora-radius-md);
    background: rgba(248, 250, 252, 0.8);
    font-size: 1rem;
    transition: all 0.3s ease;
    outline: none;
    cursor: pointer;
}

.aurora-form-textarea {
    width: 100%;
    padding: var(--aurora-spacing-md) var(--aurora-spacing-lg);
    border: 2px solid rgba(102, 126, 234, 0.1);
    border-radius: var(--aurora-radius-md);
    background: rgba(248, 250, 252, 0.8);
    font-size: 1rem;
    transition: all 0.3s ease;
    outline: none;
    resize: vertical;
    min-height: 120px;
}

/* Aurora Badge Component */
.aurora-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--aurora-spacing-xs) var(--aurora-spacing-md);
    background: var(--aurora-primary);
    color: white;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.aurora-badge-secondary {
    background: var(--aurora-secondary);
}

.aurora-badge-success {
    background: var(--aurora-success);
}

.aurora-badge-warning {
    background: var(--aurora-warning);
}

.aurora-badge-danger {
    background: var(--aurora-danger);
}

/* Aurora Alert Component */
.aurora-alert {
    padding: var(--aurora-spacing-lg);
    border-radius: var(--aurora-radius-md);
    margin-bottom: var(--aurora-spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--aurora-spacing-md);
}

.aurora-alert-success {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.2);
    color: #16a34a;
}

.aurora-alert-danger {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: #dc2626;
}

.aurora-alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
    color: #d97706;
}

.aurora-alert-info {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    color: #2563eb;
}

/* Aurora Table Component */
.aurora-table {
    width: 100%;
    border-collapse: collapse;
    border-radius: var(--aurora-radius-lg);
    overflow: hidden;
    box-shadow: var(--aurora-shadow-md);
}

.aurora-table thead th {
    background: var(--aurora-primary);
    color: white;
    padding: var(--aurora-spacing-lg);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
}

.aurora-table tbody td {
    padding: var(--aurora-spacing-lg);
    border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    vertical-align: middle;
}

.aurora-table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

.aurora-table tbody tr:last-child td {
    border-bottom: none;
}

/* Aurora Stat Card Component */
.aurora-stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--aurora-radius-xl);
    padding: var(--aurora-spacing-xl);
    box-shadow: var(--aurora-shadow-lg);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.aurora-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--aurora-primary);
}

.aurora-stat-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--aurora-shadow-xl);
}

.aurora-stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--aurora-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: var(--aurora-spacing-lg);
    box-shadow: var(--aurora-shadow-md);
}

.aurora-stat-value {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--aurora-dark);
    margin-bottom: var(--aurora-spacing-sm);
    line-height: 1;
}

.aurora-stat-label {
    color: var(--aurora-text);
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: var(--aurora-spacing-md);
}

.aurora-stat-trend {
    font-size: 0.875rem;
    font-weight: 600;
    padding: var(--aurora-spacing-xs) var(--aurora-spacing-md);
    border-radius: 20px;
    display: inline-flex;
    align-items: center;
    gap: var(--aurora-spacing-xs);
}

.aurora-stat-trend.up {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
}

.aurora-stat-trend.down {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

/* Responsive Design */
@media (max-width: 768px) {
    .aurora-card-header,
    .aurora-card-body,
    .aurora-card-footer {
        padding: var(--aurora-spacing-lg);
    }
    
    .aurora-stat-value {
        font-size: 2rem;
    }
    
    .aurora-btn {
        padding: var(--aurora-spacing-sm) var(--aurora-spacing-md);
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .aurora-card-header,
    .aurora-card-body,
    .aurora-card-footer {
        padding: var(--aurora-spacing-md);
    }
    
    .aurora-stat-value {
        font-size: 1.75rem;
    }
    
    .aurora-stat-icon {
        width: 48px;
        height: 48px;
        font-size: 1.25rem;
    }
}
