using System.ComponentModel.DataAnnotations;

namespace THLTW_B2.Models.ViewModels
{
    public class ProductViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Tên sản phẩm là bắt buộc")]
        [StringLength(100, ErrorMessage = "Tên sản phẩm không được quá 100 ký tự")]
        [Display(Name = "Tên sản phẩm")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "Giá sản phẩm là bắt buộc")]
        [Range(0.01, 10000000, ErrorMessage = "Giá phải từ 0.01 đến 10,000,000")]
        [Display(Name = "Giá")]
        public decimal Price { get; set; }

        [Display(Name = "Mô tả")]
        [StringLength(1000, ErrorMessage = "<PERSON><PERSON> tả không được quá 1000 ký tự")]
        public string Description { get; set; } = string.Empty;

        [Required(ErrorMessage = "Danh mục là bắt buộc")]
        [Display(Name = "Danh mục")]
        public int CategoryId { get; set; }

        [Display(Name = "Hình ảnh hiện tại")]
        public string? ImageUrl { get; set; }

        [Display(Name = "Upload hình ảnh mới")]
        public IFormFile? ImageFile { get; set; }

        [Display(Name = "URL hình ảnh")]
        public string? ImageUrlInput { get; set; }

        // Navigation properties
        public Category? Category { get; set; }
    }
}
