﻿@model IEnumerable<THLTW_B2.Models.Product>
@{
    ViewData["Title"] = "Quản lý sản phẩm";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<style>
    .aurora-product-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .aurora-product-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .aurora-product-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .aurora-table {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: none;
        border: none;
    }

    .aurora-table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1rem;
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .aurora-table tbody td {
        padding: 1rem;
        border: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        vertical-align: middle;
    }

    .aurora-table tbody tr:hover {
        background: rgba(102, 126, 234, 0.05);
    }

    .aurora-btn-group .btn {
        border-radius: 8px;
        margin: 0 2px;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
        border: none;
        transition: all 0.3s ease;
    }

    .aurora-btn-info {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
    }

    .aurora-btn-warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
    }

    .aurora-btn-danger {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    }

    .aurora-btn-group .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .aurora-empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #64748b;
    }

    .aurora-empty-icon {
        font-size: 4rem;
        color: #cbd5e1;
        margin-bottom: 1.5rem;
    }

    .aurora-badge {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.375rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
</style>

<div class="aurora-product-header">
    <div class="d-flex flex-column flex-md-row align-items-md-center justify-content-between">
        <div>
            <h1 style="font-size: 2.5rem; font-weight: 800; color: #1a1d29; margin-bottom: 0.5rem;">
                <i class="fas fa-cube me-3" style="color: #667eea;"></i>Quản lý sản phẩm
            </h1>
            <p style="color: #64748b; font-size: 1.1rem; margin: 0;">
                Quản lý toàn bộ sản phẩm trong hệ thống của bạn
            </p>
        </div>
        @if (User.IsInRole("Admin"))
        {
            <div class="mt-3 mt-md-0">
                <a asp-action="Add" class="aurora-btn" style="background: var(--aurora-success); box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);">
                    <i class="fas fa-plus me-2"></i>Thêm sản phẩm mới
                </a>
            </div>
        }
    </div>
</div>

@if (TempData["Success"] != null)
{
    <div style="background: rgba(34, 197, 94, 0.1); border: 1px solid rgba(34, 197, 94, 0.2); border-radius: 12px; padding: 1rem; margin-bottom: 2rem; color: #16a34a;">
        <i class="fas fa-check-circle me-2"></i>@TempData["Success"]
    </div>
}

@if (TempData["Error"] != null)
{
    <div style="background: rgba(239, 68, 68, 0.1); border: 1px solid rgba(239, 68, 68, 0.2); border-radius: 12px; padding: 1rem; margin-bottom: 2rem; color: #dc2626;">
        <i class="fas fa-exclamation-circle me-2"></i>@TempData["Error"]
    </div>
}

<div class="aurora-product-card">
    <div class="card-body p-0">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table aurora-table mb-0">
                    <thead>
                        <tr>
                            <th>Hình ảnh</th>
                            <th>Tên sản phẩm</th>
                            <th>Giá</th>
                            <th>Danh mục</th>
                            <th>Mô tả</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var product in Model)
                        {
                            <tr>
                                <td>
                                    @if (!string.IsNullOrEmpty(product.ImageUrl))
                                    {
                                        <img src="@product.ImageUrl" alt="@product.Name"
                                             style="width: 60px; height: 60px; object-fit: cover; border-radius: 12px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
                                        <div style="width: 60px; height: 60px; background: rgba(102, 126, 234, 0.1); border-radius: 12px; display: none; align-items: center; justify-content: center;">
                                            <i class="fas fa-image" style="color: #667eea;"></i>
                                        </div>
                                    }
                                    else
                                    {
                                        <div style="width: 60px; height: 60px; background: rgba(102, 126, 234, 0.1); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-image" style="color: #667eea;"></i>
                                        </div>
                                    }
                                </td>
                                <td>
                                    <div>
                                        <strong style="color: #1a1d29; font-weight: 600;">@product.Name</strong>
                                    </div>
                                </td>
                                <td>
                                    <span style="color: #16a34a; font-weight: 700; font-size: 1.1rem;">
                                        @product.Price.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))
                                    </span>
                                </td>
                                <td>
                                    @if (product.Category != null)
                                    {
                                        <span class="aurora-badge">@product.Category.Name</span>
                                    }
                                    else
                                    {
                                        <span style="color: #94a3b8; font-style: italic;">Chưa phân loại</span>
                                    }
                                </td>
                                <td>
                                    <div style="color: #64748b;">
                                        @if (product.Description.Length > 50)
                                        {
                                            <span title="@product.Description">@(product.Description.Substring(0, 50))...</span>
                                        }
                                        else
                                        {
                                            @product.Description
                                        }
                                    </div>
                                </td>
                                <td>
                                    <div class="aurora-btn-group" role="group">
                                        <a asp-action="Display" asp-route-id="@product.Id" class="btn aurora-btn-info" title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if (User.IsInRole("Admin"))
                                        {
                                            <a asp-action="Update" asp-route-id="@product.Id" class="btn aurora-btn-warning" title="Chỉnh sửa">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a asp-action="Delete" asp-route-id="@product.Id" class="btn aurora-btn-danger" title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="aurora-empty-state">
                <div class="aurora-empty-icon">
                    <i class="fas fa-cube"></i>
                </div>
                <h3 style="color: #1a1d29; font-weight: 700; margin-bottom: 1rem;">
                    Chưa có sản phẩm nào
                </h3>
                <p style="color: #64748b; font-size: 1.1rem; margin-bottom: 2rem;">
                    Hãy thêm sản phẩm đầu tiên để bắt đầu quản lý cửa hàng của bạn
                </p>
                @if (User.IsInRole("Admin"))
                {
                    <a asp-action="Add" class="aurora-btn" style="background: var(--aurora-success); box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);">
                        <i class="fas fa-plus me-2"></i>Thêm sản phẩm mới
                    </a>
                }
            </div>
        }
    </div>
</div>
