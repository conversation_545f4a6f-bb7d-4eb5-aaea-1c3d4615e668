html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

/* Animation và style cho trang đăng nhập hiện đại */
@keyframes bgfade {
    from { filter: blur(8px) brightness(0.7); }
    to { filter: blur(0) brightness(1); }
}
@keyframes cardfade {
    0% { transform: translateY(60px) scale(0.95); opacity: 0; }
    100% { transform: translateY(0) scale(1); opacity: 1; }
}
@keyframes shake {
    10%, 90% { transform: translateX(-2px); }
    20%, 80% { transform: translateX(4px); }
    30%, 50%, 70% { transform: translateX(-8px); }
    40%, 60% { transform: translateX(8px); }
}
body.login-bg {
    background: linear-gradient(135deg, #232526 0%, #414345 100%);
    min-height: 100vh;
    animation: bgfade 2s;
}
.login-card {
    background: rgba(255,255,255,0.95);
    box-shadow: 0 8px 32px 0 rgba(31,38,135,0.37);
    border-radius: 2rem;
    border: 1px solid rgba(255,255,255,0.18);
    animation: cardfade 1.2s cubic-bezier(.68,-0.55,.27,1.55);
}
.login-card .form-control {
    background: #f8fafc;
    border: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    transition: box-shadow 0.3s;
}
.login-card .form-control:focus {
    box-shadow: 0 4px 16px rgba(0,0,0,0.10);
}
.login-card .btn-primary {
    background: linear-gradient(90deg, #232526 0%, #414345 100%);
    border: none;
    transition: background 0.3s, transform 0.2s;
}
.login-card .btn-primary:hover {
    background: linear-gradient(90deg, #414345 0%, #232526 100%);
    transform: scale(1.03);
}
.login-card .btn-outline-danger {
    border: 2px solid #e74c3c;
    color: #e74c3c;
    background: #fff;
    transition: background 0.3s, color 0.3s, transform 0.2s;
}
.login-card .btn-outline-danger:hover {
    background: #e74c3c;
    color: #fff;
    transform: scale(1.03);
}
.login-card .form-label {
    color: #232526;
}
.login-card .form-check-label {
    color: #414345;
}
.login-card .alert-danger {
    animation: shake 0.4s;
}

/* --- Modern Admin Dashboard Styles --- */
.admin-dashboard-bg {
    background: #f5f7fa;
    min-height: 100vh;
}
.admin-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: #22223b;
    letter-spacing: 1px;
}
.admin-subtitle {
    color: #4a5568;
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}
.admin-card {
    border-radius: 1.25rem;
    border: none;
    background: #fff;
    transition: box-shadow 0.2s, transform 0.2s;
    box-shadow: 0 2px 12px rgba(34, 34, 59, 0.06);
}
.admin-card:hover, .admin-card:focus-within {
    box-shadow: 0 8px 32px rgba(34, 34, 59, 0.13);
    transform: translateY(-2px) scale(1.01);
}
.admin-icon-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 2rem;
    color: #fff;
    box-shadow: 0 4px 16px rgba(34, 34, 59, 0.10);
    margin: 0 auto 1rem auto;
}
.bg-primary { background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important; }
.bg-success { background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%) !important; }
.bg-info { background: linear-gradient(135deg, #06b6d4 0%, #0ea5e9 100%) !important; }
.admin-card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #22223b;
    margin-bottom: 0.5rem;
}
.admin-card-desc {
    color: #6b7280;
    font-size: 1rem;
    margin-bottom: 1.2rem;
}
.admin-btn-primary {
    background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
    color: #fff;
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    padding: 0.75rem 1.5rem;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.10);
    transition: background 0.2s, box-shadow 0.2s;
}
.admin-btn-primary:hover, .admin-btn-primary:focus {
    background: linear-gradient(90deg, #2563eb 0%, #3b82f6 100%);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.18);
}
.admin-btn-success {
    background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);
    color: #fff;
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    padding: 0.75rem 1.5rem;
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.10);
    transition: background 0.2s, box-shadow 0.2s;
}
.admin-btn-success:hover, .admin-btn-success:focus {
    background: linear-gradient(90deg, #16a34a 0%, #22c55e 100%);
    box-shadow: 0 4px 16px rgba(34, 197, 94, 0.18);
}
.admin-btn-info {
    background: linear-gradient(90deg, #06b6d4 0%, #0ea5e9 100%);
    color: #fff;
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    padding: 0.75rem 1.5rem;
    box-shadow: 0 2px 8px rgba(6, 182, 212, 0.10);
    transition: background 0.2s, box-shadow 0.2s;
}
.admin-btn-info:hover, .admin-btn-info:focus {
    background: linear-gradient(90deg, #0ea5e9 0%, #06b6d4 100%);
    box-shadow: 0 4px 16px rgba(6, 182, 212, 0.18);
}
.admin-alert-info {
    background: #e0e7ef;
    color: #22223b;
    border-radius: 0.75rem;
    font-size: 1rem;
    font-weight: 500;
    border: none;
    box-shadow: 0 2px 8px rgba(34, 34, 59, 0.06);
    padding: 1.25rem 1rem;
}
@media (max-width: 768px) {
    .admin-title { font-size: 1.5rem; }
    .admin-card-title { font-size: 1.05rem; }
    .admin-card-desc { font-size: 0.95rem; }
    .admin-icon-badge { width: 48px; height: 48px; font-size: 1.5rem; }
}
@media (max-width: 576px) {
    .admin-dashboard-bg { padding: 1rem 0; }
    .admin-title { font-size: 1.1rem; }
    .admin-card { border-radius: 1rem; }
    .admin-btn-primary, .admin-btn-success, .admin-btn-info { font-size: 0.95rem; padding: 0.6rem 1rem; }
}
/* --- End Modern Admin Dashboard Styles --- */

/* --- AccessDenied Page Styles --- */
.accessdenied-bg {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
    position: relative;
    overflow: hidden;
    min-height: 100vh;
}
.accessdenied-bg-decoration {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    opacity: 0.7;
}
.accessdenied-bg-decoration-1 {
    top: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, rgba(251, 191, 36, 0.1), rgba(245, 158, 11, 0.1));
}
.accessdenied-bg-decoration-2 {
    bottom: -100px;
    left: -100px;
    width: 300px;
    height: 300px;
    background: linear-gradient(45deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
}
.accessdenied-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: fadeInUp 0.6s ease-out;
}
.shadow-2xl {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15) !important;
}
.accessdenied-icon-bg {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    box-shadow: 0 20px 40px rgba(251, 191, 36, 0.3);
}
.accessdenied-icon {
    font-size: 3.5rem;
}
.accessdenied-title {
    font-weight: 700;
    font-size: 2.5rem;
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
.accessdenied-lead {
    font-weight: 500;
    color: #64748b !important;
}
.accessdenied-desc {
    color: #94a3b8 !important;
    line-height: 1.6;
}
.accessdenied-btn-main {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: white;
    border: none;
    box-shadow: 0 10px 25px rgba(30, 41, 59, 0.3);
    transition: all 0.3s ease;
}
.accessdenied-btn-main:hover, .accessdenied-btn-main:focus {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(30, 41, 59, 0.4);
    color: #fbbf24;
}
.accessdenied-btn-outline {
    background: transparent;
    color: #475569;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}
.accessdenied-btn-outline:hover, .accessdenied-btn-outline:focus {
    background: #f8fafc;
    border-color: #cbd5e1;
    color: #b91c1c;
    transform: translateY(-2px);
}
.accessdenied-help {
    border-top: 1px solid #f1f5f9;
}
.accessdenied-help-link {
    color: #3b82f6;
}
.accessdenied-help-link:hover {
    color: #1e40af;
}
.accessdenied-help-icon {
    color: #94a3b8;
}
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
@media (max-width: 576px) {
    .card-body {
        padding: 2rem !important;
    }
    .accessdenied-title {
        font-size: 2rem !important;
    }
    .btn-lg {
        padding: 0.75rem 2rem !important;
    }
}
/* --- End AccessDenied Page Styles --- */

/* --- Luxury Admin Dashboard Styles --- */
.admin-dashboard-wrapper {
    background: #f7f8fa;
    min-height: 100vh;
    padding: 0;
}
.admin-dashboard-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem 2.5rem 2rem 2.5rem;
}
.admin-dashboard-header {
    border-bottom: 1px solid #ececec;
    padding-bottom: 1.5rem;
    margin-bottom: 2rem;
}
.admin-title {
    font-size: 2.3rem;
    font-weight: 800;
    color: #18181b;
    margin-bottom: 0.2rem;
}
.admin-subtitle {
    color: #64748b;
    font-size: 1.1rem;
    font-weight: 500;
}
.admin-search-box {
    background: #f1f5f9;
    border-radius: 2rem;
    padding: 0.25rem 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}
.admin-search-input {
    border: none;
    background: transparent;
    outline: none;
    font-size: 1rem;
    padding: 0.5rem 0.75rem;
    width: 180px;
}
.admin-search-btn {
    background: none;
    border: none;
    color: #64748b;
    font-size: 1.1rem;
    margin-left: 0.5rem;
}
/* Stat Cards */
.admin-stat-card {
    background: #fff;
    border-radius: 1.25rem;
    box-shadow: 0 2px 16px rgba(30,41,59,0.07);
    padding: 1.5rem 1.25rem 1.25rem 1.25rem;
    position: relative;
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    margin-bottom: 0.5rem;
}
.admin-stat-label {
    color: #64748b;
    font-size: 1.05rem;
    font-weight: 500;
    margin-bottom: 0.2rem;
}
.admin-stat-value {
    font-size: 2.1rem;
    font-weight: 800;
    color: #18181b;
    margin-bottom: 0.2rem;
}
.admin-stat-trend {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.2rem;
}
.admin-stat-trend.up { color: #22c55e; }
.admin-stat-trend.down { color: #ef4444; }
.admin-stat-icon {
    position: absolute;
    top: 1.25rem;
    right: 1.25rem;
    width: 38px;
    height: 38px;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    color: #fff;
    opacity: 0.95;
}
.bg-blue { background: #3b82f6 !important; }
.bg-green { background: #22c55e !important; }
.bg-purple { background: #a78bfa !important; }
.bg-orange { background: #fbbf24 !important; }
/* Quick Action Cards */
.admin-section-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #18181b;
}
.admin-quick-card {
    background: #fff;
    border-radius: 1.25rem;
    box-shadow: 0 2px 16px rgba(30,41,59,0.07);
    padding: 1.5rem 1.25rem 1.25rem 1.25rem;
    display: flex;
    gap: 1.25rem;
    align-items: flex-start;
    min-height: 160px;
    transition: box-shadow 0.2s, transform 0.2s;
}
.admin-quick-card:hover {
    box-shadow: 0 8px 32px rgba(30,41,59,0.13);
    transform: translateY(-2px) scale(1.01);
}
.admin-quick-icon {
    width: 48px;
    height: 48px;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #fff;
    margin-top: 0.2rem;
    flex-shrink: 0;
}
.admin-btn-blue {
    background: #2563eb;
    color: #fff;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    transition: background 0.2s, box-shadow 0.2s;
}
.admin-btn-blue:hover, .admin-btn-blue:focus {
    background: #1d4ed8;
    color: #fff;
}
.admin-btn-green {
    background: #16a34a;
    color: #fff;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    transition: background 0.2s, box-shadow 0.2s;
}
.admin-btn-green:hover, .admin-btn-green:focus {
    background: #15803d;
    color: #fff;
}
.admin-btn-purple {
    background: #a78bfa;
    color: #fff;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    transition: background 0.2s, box-shadow 0.2s;
}
.admin-btn-purple:hover, .admin-btn-purple:focus {
    background: #7c3aed;
    color: #fff;
}
.admin-quick-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: #18181b;
    margin-bottom: 0.2rem;
}
.admin-quick-desc {
    color: #64748b;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}
/* Info Card */
.admin-info-card {
    background: #f1f5f9;
    border-radius: 1.25rem;
    box-shadow: 0 2px 8px rgba(30,41,59,0.04);
    padding: 1.25rem 1.25rem 1.25rem 1.25rem;
    display: flex;
    align-items: flex-start;
    gap: 1.25rem;
}
.admin-info-icon {
    width: 38px;
    height: 38px;
    border-radius: 1rem;
    background: #e0e7ef;
    color: #2563eb;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    margin-top: 0.2rem;
    flex-shrink: 0;
}
.admin-info-title {
    font-size: 1.05rem;
    font-weight: 700;
    color: #18181b;
    margin-bottom: 0.2rem;
}
.admin-info-desc {
    color: #64748b;
    font-size: 1rem;
}
/* Responsive */
@media (max-width: 1200px) {
    .admin-dashboard-content { padding: 2rem 1rem; }
}
@media (max-width: 768px) {
    .admin-dashboard-content { padding: 1rem 0.5rem; }
    .admin-title { font-size: 1.5rem; }
    .admin-stat-value { font-size: 1.3rem; }
    .admin-quick-title { font-size: 1rem; }
    .admin-quick-card, .admin-stat-card { padding: 1rem; }
}
@media (max-width: 576px) {
    .admin-dashboard-content { padding: 0.5rem 0.2rem; }
    .admin-title { font-size: 1.1rem; }
    .admin-stat-card, .admin-quick-card, .admin-info-card { border-radius: 0.8rem; }
    .admin-stat-value { font-size: 1.1rem; }
}
/* --- End Luxury Admin Dashboard Styles --- */