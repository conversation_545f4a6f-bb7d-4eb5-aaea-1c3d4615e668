@using Microsoft.AspNetCore.Identity
@inject UserManager<THLTW_B2.Models.ApplicationUser> UserManager
@{
    Layout = null;
    var userName = User.Identity?.Name ?? "Admin";
}
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Aurora Admin - @ViewData["Title"]</title>
    <link rel="stylesheet" href="~/lib/bootstrap/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />
    <link rel="stylesheet" href="~/css/site.css" />
    <link rel="stylesheet" href="~/css/aurora.css" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --aurora-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --aurora-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --aurora-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --aurora-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --aurora-dark: #1a1d29;
            --aurora-darker: #151821;
            --aurora-light: #f8fafc;
            --aurora-text: #64748b;
            --aurora-text-dark: #334155;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Inter', sans-serif;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .sidebar {
            min-height: 100vh;
            background: rgba(26, 29, 41, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            color: #fff;
            width: 280px;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
            box-shadow: 0 0 50px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar-brand {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.5rem;
        }

        .sidebar-brand-icon {
            width: 40px;
            height: 40px;
            background: var(--aurora-primary);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
        }

        .sidebar-brand-text {
            font-size: 1.25rem;
            font-weight: 700;
            color: white;
            letter-spacing: -0.025em;
        }

        .sidebar-user {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            margin-top: 1rem;
        }

        .sidebar-user-avatar {
            width: 32px;
            height: 32px;
            background: var(--aurora-secondary);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            color: white;
        }

        .sidebar-user-info h6 {
            color: white;
            font-size: 0.875rem;
            font-weight: 600;
            margin: 0;
        }

        .sidebar-user-info small {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.75rem;
        }

        .sidebar-nav {
            padding: 1.5rem 0;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.7);
            padding: 0.875rem 1.5rem;
            border-radius: 0;
            margin: 0;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            position: relative;
        }

        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--aurora-primary);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(8px);
        }

        .sidebar .nav-link:hover::before, .sidebar .nav-link.active::before {
            transform: scaleY(1);
        }

        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1rem;
        }

        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
            background: rgba(248, 250, 252, 0.95);
            backdrop-filter: blur(20px);
        }

        .aurora-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .aurora-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .topbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            object-fit: cover;
            border: 2px solid transparent;
            background: var(--aurora-primary);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        @@media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 1rem;
            }
        }
    </style>
    @RenderSection("Styles", required: false)
</head>
<body>
    <div class="sidebar d-flex flex-column">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <div class="sidebar-brand-icon">
                    <i class="fas fa-gem"></i>
                </div>
                <div class="sidebar-brand-text">Aurora Admin</div>
            </div>
            <div class="sidebar-user">
                <div class="sidebar-user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="sidebar-user-info">
                    <h6>@userName</h6>
                    <small>Administrator</small>
                </div>
            </div>
        </div>
        <nav class="nav flex-column sidebar-nav">
            <a class="nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Index" ? "active" : "")" asp-controller="Admin" asp-action="Index">
                <i class="fas fa-chart-line"></i>
                <span>Dashboard</span>
            </a>
            <a class="nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Users" ? "active" : "")" asp-controller="Admin" asp-action="Users">
                <i class="fas fa-users"></i>
                <span>Quản lý người dùng</span>
            </a>
            <a class="nav-link" asp-controller="Product" asp-action="Index">
                <i class="fas fa-cube"></i>
                <span>Sản phẩm</span>
            </a>
            <a class="nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Components" ? "active" : "")" asp-controller="Admin" asp-action="Components">
                <i class="fas fa-palette"></i>
                <span>Aurora Components</span>
            </a>
            <a class="nav-link" asp-controller="Account" asp-action="Logout">
                <i class="fas fa-sign-out-alt"></i>
                <span>Đăng xuất</span>
            </a>
        </nav>
    </div>
    <div class="main-content">
        <div class="topbar d-flex align-items-center justify-content-between mb-4" style="background:#fff; border-bottom:1px solid #e3e6f0; padding:1.5rem 2rem; border-radius:1rem; box-shadow:0 2px 12px rgba(34,34,59,0.06);">
            <div class="d-flex align-items-center gap-3">
                <div class="d-flex flex-column">
                    <span class="fw-bold fs-5 text-dark">Xin chào, @userName</span>
                    <small class="text-secondary">Chào mừng trở lại với trang quản trị</small>
                </div>
            </div>
            <div class="d-flex align-items-center gap-3">
                @{
                    var currentUser = UserManager.GetUserAsync(User).Result;
                    var avatarUrl = currentUser?.AvatarUrl ?? Url.Content("~/images/placeholder.svg");
                }
                <img src="@avatarUrl" alt="Avatar" class="avatar shadow-sm border border-2" style="width:48px; height:48px; border-radius:50%; object-fit:cover;" />
            </div>
        </div>
        <div class="container-fluid">
            @RenderBody()
        </div>
    </div>
    <script src="~/lib/jquery/jquery.min.js"></script>
    <script src="~/lib/bootstrap/js/bootstrap.bundle.min.js"></script>
    @RenderSection("Scripts", required: false)
</body>
</html>
