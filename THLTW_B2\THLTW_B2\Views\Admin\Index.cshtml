@{
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
    ViewData["Title"] = "Dashboard";
}

<style>
    .aurora-dashboard-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .aurora-stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 1.5rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .aurora-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--aurora-primary);
    }

    .aurora-stat-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .aurora-stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .aurora-stat-value {
        font-size: 2.5rem;
        font-weight: 800;
        color: #1a1d29;
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .aurora-stat-label {
        color: #64748b;
        font-size: 1rem;
        font-weight: 500;
        margin-bottom: 0.75rem;
    }

    .aurora-stat-trend {
        font-size: 0.875rem;
        font-weight: 600;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        display: inline-block;
    }

    .aurora-stat-trend.up {
        background: rgba(34, 197, 94, 0.1);
        color: #16a34a;
    }

    .aurora-quick-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 1.5rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;
    }

    .aurora-quick-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .aurora-btn {
        background: var(--aurora-primary);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .aurora-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .aurora-btn-success {
        background: var(--aurora-success);
        box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
    }

    .aurora-btn-warning {
        background: var(--aurora-warning);
        box-shadow: 0 4px 15px rgba(67, 233, 123, 0.3);
    }

    .aurora-info-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 1.5rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #3b82f6;
    }
</style>

<div class="aurora-dashboard-header">
    <div class="d-flex flex-column flex-md-row align-items-md-center justify-content-between">
        <div>
            <h1 style="font-size: 2.5rem; font-weight: 800; color: #1a1d29; margin-bottom: 0.5rem;">
                Quản trị hệ thống
            </h1>
            <p style="color: #64748b; font-size: 1.1rem; margin: 0;">
                Chào mừng bạn đến với Aurora Admin Dashboard
            </p>
        </div>
        <div class="d-none d-md-block">
            <div style="background: rgba(102, 126, 234, 0.1); padding: 0.75rem 1.5rem; border-radius: 12px;">
                <i class="fas fa-calendar-alt" style="color: #667eea; margin-right: 0.5rem;"></i>
                <span style="color: #667eea; font-weight: 600;">@DateTime.Now.ToString("dd/MM/yyyy")</span>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Section -->
<div class="d-flex align-items-center justify-content-between mb-4">
    <h2 style="font-size: 1.75rem; font-weight: 700; color: #1a1d29; margin: 0;">
        Thao tác nhanh
    </h2>
    <div style="background: rgba(102, 126, 234, 0.1); padding: 0.5rem 1rem; border-radius: 8px;">
        <span style="color: #667eea; font-weight: 600; font-size: 0.875rem;">
            <i class="fas fa-bolt me-1"></i>Quick Actions
        </span>
    </div>
</div>

<div class="row g-4 mb-5">
    <div class="col-12 col-md-6 col-lg-4">
        <div class="aurora-quick-card">
            <div class="d-flex align-items-center mb-3">
                <div class="aurora-stat-icon me-3" style="background: var(--aurora-primary); width: 50px; height: 50px; font-size: 1.25rem;">
                    <i class="fas fa-users"></i>
                </div>
                <div>
                    <h5 style="color: #1a1d29; font-weight: 700; margin: 0;">Quản lý người dùng</h5>
                </div>
            </div>
            <p style="color: #64748b; margin-bottom: 1.5rem;">
                Xem danh sách, kích hoạt và vô hiệu hóa tài khoản, phân quyền người dùng trong hệ thống
            </p>
            <a asp-action="Users" class="aurora-btn w-100">
                Truy cập <i class="fas fa-arrow-right ms-2"></i>
            </a>
        </div>
    </div>

    <div class="col-12 col-md-6 col-lg-4">
        <div class="aurora-quick-card">
            <div class="d-flex align-items-center mb-3">
                <div class="aurora-stat-icon me-3" style="background: var(--aurora-success); width: 50px; height: 50px; font-size: 1.25rem;">
                    <i class="fas fa-cube"></i>
                </div>
                <div>
                    <h5 style="color: #1a1d29; font-weight: 700; margin: 0;">Quản lý sản phẩm</h5>
                </div>
            </div>
            <p style="color: #64748b; margin-bottom: 1.5rem;">
                Thêm, sửa, xóa sản phẩm và quản lý danh mục sản phẩm một cách dễ dàng
            </p>
            <a asp-controller="Product" asp-action="Index" class="aurora-btn aurora-btn-success w-100">
                Truy cập <i class="fas fa-arrow-right ms-2"></i>
            </a>
        </div>
    </div>

    <div class="col-12 col-md-6 col-lg-4">
        <div class="aurora-quick-card">
            <div class="d-flex align-items-center mb-3">
                <div class="aurora-stat-icon me-3" style="background: var(--aurora-warning); width: 50px; height: 50px; font-size: 1.25rem;">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div>
                    <h5 style="color: #1a1d29; font-weight: 700; margin: 0;">Thống kê & Báo cáo</h5>
                </div>
            </div>
            <p style="color: #64748b; margin-bottom: 1.5rem;">
                Xem báo cáo chi tiết và thống kê hoạt động của toàn bộ hệ thống
            </p>
            <button class="aurora-btn aurora-btn-warning w-100" disabled>
                Sắp ra mắt <i class="fas fa-clock ms-2"></i>
            </button>
        </div>
    </div>
</div>

<!-- Important Notice -->
<div class="aurora-info-card">
    <div class="d-flex align-items-start">
        <div style="width: 50px; height: 50px; background: rgba(59, 130, 246, 0.1); border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 1rem; flex-shrink: 0;">
            <i class="fas fa-info-circle" style="color: #3b82f6; font-size: 1.25rem;"></i>
        </div>
        <div>
            <h5 style="color: #1a1d29; font-weight: 700; margin-bottom: 0.5rem;">
                Lưu ý quan trọng
            </h5>
            <p style="color: #64748b; margin: 0;">
                Bạn đang đăng nhập với quyền quản trị viên. Hãy cẩn thận khi thực hiện các thao tác có thể ảnh hưởng đến dữ liệu hệ thống.
            </p>
        </div>
    </div>
</div>
