using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using THLTW_B2.DataAccess;
using THLTW_B2.Models;

namespace THLTW_B2.Services
{
    public class SeedDataService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SeedDataService> _logger;

        public SeedDataService(
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole> roleManager,
            ApplicationDbContext context,
            ILogger<SeedDataService> logger)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _context = context;
            _logger = logger;
        }

        public async Task SeedAsync()
        {
            try
            {
                // Tạo roles
                await CreateRoleIfNotExists("Admin");
                await CreateRoleIfNotExists("User");

                // Tạo admin mặc định
                await CreateDefaultAdmin();

                // Tạo categories mẫu
                await CreateSampleCategories();

                // Tạo sản phẩm mẫu
                await CreateSampleProducts();

                _logger.LogInformation("Seed data completed successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while seeding data.");
            }
        }

        private async Task CreateRoleIfNotExists(string roleName)
        {
            if (!await _roleManager.RoleExistsAsync(roleName))
            {
                var role = new IdentityRole(roleName);
                var result = await _roleManager.CreateAsync(role);
                
                if (result.Succeeded)
                {
                    _logger.LogInformation("Role {RoleName} created successfully.", roleName);
                }
                else
                {
                    _logger.LogError("Failed to create role {RoleName}: {Errors}", 
                        roleName, string.Join(", ", result.Errors.Select(e => e.Description)));
                }
            }
        }

        private async Task CreateDefaultAdmin()
        {
            const string adminEmail = "<EMAIL>";
            const string adminPassword = "Admin123!";

            var adminUser = await _userManager.FindByEmailAsync(adminEmail);
            
            if (adminUser == null)
            {
                adminUser = new ApplicationUser
                {
                    UserName = adminEmail,
                    Email = adminEmail,
                    FullName = "Quản trị viên hệ thống",
                    EmailConfirmed = true,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };

                var result = await _userManager.CreateAsync(adminUser, adminPassword);

                if (result.Succeeded)
                {
                    await _userManager.AddToRoleAsync(adminUser, "Admin");
                    await _userManager.AddToRoleAsync(adminUser, "User");
                    
                    _logger.LogInformation("Default admin user created successfully with email: {Email}", adminEmail);
                    _logger.LogInformation("Default admin password: {Password}", adminPassword);
                }
                else
                {
                    _logger.LogError("Failed to create default admin user: {Errors}", 
                        string.Join(", ", result.Errors.Select(e => e.Description)));
                }
            }
            else
            {
                // Đảm bảo admin có đủ quyền
                if (!await _userManager.IsInRoleAsync(adminUser, "Admin"))
                {
                    await _userManager.AddToRoleAsync(adminUser, "Admin");
                    _logger.LogInformation("Added Admin role to existing user: {Email}", adminEmail);
                }
                
                if (!await _userManager.IsInRoleAsync(adminUser, "User"))
                {
                    await _userManager.AddToRoleAsync(adminUser, "User");
                    _logger.LogInformation("Added User role to existing user: {Email}", adminEmail);
                }
            }
        }

        private async Task CreateSampleCategories()
        {
            if (!_context.Categories.Any())
            {
                var categories = new List<Category>
                {
                    new Category { Name = "Điện thoại" },
                    new Category { Name = "Laptop" },
                    new Category { Name = "Tablet" },
                    new Category { Name = "Phụ kiện" },
                    new Category { Name = "Đồng hồ thông minh" },
                    new Category { Name = "Tai nghe" }
                };

                _context.Categories.AddRange(categories);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Sample categories created successfully.");
            }
        }

        private async Task CreateSampleProducts()
        {
            if (!_context.Products.Any())
            {
                var categories = await _context.Categories.ToListAsync();
                if (categories.Any())
                {
                    var products = new List<Product>
                    {
                        new Product
                        {
                            Name = "iPhone 15 Pro Max",
                            Price = 29990000,
                            Description = "iPhone 15 Pro Max với chip A17 Pro, camera 48MP và màn hình Super Retina XDR 6.7 inch.",
                            CategoryId = categories.First(c => c.Name == "Điện thoại").Id,
                            ImageUrl = "https://cdn.tgdd.vn/Products/Images/42/305658/iphone-15-pro-max-blue-thumbnew-600x600.jpg"
                        },
                        new Product
                        {
                            Name = "MacBook Air M2",
                            Price = 27990000,
                            Description = "MacBook Air với chip M2, màn hình Liquid Retina 13.6 inch và thời lượng pin lên đến 18 giờ.",
                            CategoryId = categories.First(c => c.Name == "Laptop").Id,
                            ImageUrl = "https://cdn.tgdd.vn/Products/Images/44/282827/macbook-air-m2-2022-den-600x600.jpg"
                        },
                        new Product
                        {
                            Name = "iPad Pro 12.9 inch",
                            Price = 24990000,
                            Description = "iPad Pro với chip M2, màn hình Liquid Retina XDR 12.9 inch và hỗ trợ Apple Pencil thế hệ 2.",
                            CategoryId = categories.First(c => c.Name == "Tablet").Id,
                            ImageUrl = "https://cdn.tgdd.vn/Products/Images/522/247508/ipad-pro-129-inch-wifi-128gb-2022-xam-600x600.jpg"
                        },
                        new Product
                        {
                            Name = "AirPods Pro 2",
                            Price = 5990000,
                            Description = "AirPods Pro thế hệ 2 với chip H2, chống ồn chủ động và âm thanh không gian cá nhân hóa.",
                            CategoryId = categories.First(c => c.Name == "Tai nghe").Id,
                            ImageUrl = "https://cdn.tgdd.vn/Products/Images/54/289780/airpods-pro-2nd-gen-600x600.jpg"
                        },
                        new Product
                        {
                            Name = "Apple Watch Series 9",
                            Price = 8990000,
                            Description = "Apple Watch Series 9 với chip S9, màn hình Retina Always-On và tính năng Double Tap mới.",
                            CategoryId = categories.First(c => c.Name == "Đồng hồ thông minh").Id,
                            ImageUrl = "https://cdn.tgdd.vn/Products/Images/7077/309016/apple-watch-s9-gps-41mm-pink-aluminum-light-pink-sport-band-600x600.jpg"
                        }
                    };

                    _context.Products.AddRange(products);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Sample products created successfully.");
                }
            }
        }
    }
}
