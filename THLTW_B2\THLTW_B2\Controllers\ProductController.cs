﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Threading.Tasks;
using THLTW_B2.Models;
using THLTW_B2.Models.ViewModels;
using THLTW_B2.Repositories;
using THLTW_B2.Services;

namespace THLTW_B2.Controllers
{
    public class ProductController : Controller
    {
        private readonly IProductRepository _productRepository;
        private readonly ICategoryRepository _categoryRepository;
        private readonly FileUploadService _fileUploadService;

        public ProductController(IProductRepository productRepository,
            ICategoryRepository categoryRepository,
            FileUploadService fileUploadService)
        {
            _productRepository = productRepository;
            _categoryRepository = categoryRepository;
            _fileUploadService = fileUploadService;
        }

        // GET: /Product/Add - Chỉ Admin mới được thêm sản phẩm
        [HttpGet]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Add()
        {
            var categories = await _categoryRepository.GetAllAsync();
            ViewBag.Categories = new SelectList(categories, "Id", "Name");
            return View();
        }

        // POST: /Product/Add
        [HttpPost]
        [Authorize(Roles = "Admin")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Add(ProductViewModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var product = new Product
                    {
                        Name = model.Name,
                        Price = model.Price,
                        Description = model.Description,
                        CategoryId = model.CategoryId
                    };

                    // Xử lý upload hình ảnh
                    if (model.ImageFile != null)
                    {
                        product.ImageUrl = await _fileUploadService.UploadImageAsync(model.ImageFile);
                    }
                    else if (!string.IsNullOrEmpty(model.ImageUrlInput))
                    {
                        product.ImageUrl = model.ImageUrlInput;
                    }

                    await _productRepository.AddAsync(product);
                    TempData["Success"] = "Thêm sản phẩm thành công!";
                    return RedirectToAction("Index");
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", ex.Message);
                }
            }

            // Reload categories nếu có lỗi
            var categories = await _categoryRepository.GetAllAsync();
            ViewBag.Categories = new SelectList(categories, "Id", "Name", model.CategoryId);
            return View(model);
        }

        // Display a list of products 
        public async Task<IActionResult> Index()
        {
            var products = await _productRepository.GetAllAsync();
            return View(products);
        }

        // Display a single product 
        public async Task<IActionResult> Display(int id)
        {
            var product = await _productRepository.GetByIdAsync(id);
            if (product == null)
            {
                return NotFound();
            }
            return View(product);
        }

        // GET: /Product/Update - Chỉ Admin mới được sửa sản phẩm
        [HttpGet]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Update(int id)
        {
            var product = await _productRepository.GetByIdAsync(id);
            if (product == null)
            {
                TempData["Error"] = "Không tìm thấy sản phẩm!";
                return RedirectToAction("Index");
            }

            var model = new ProductViewModel
            {
                Id = product.Id,
                Name = product.Name,
                Price = product.Price,
                Description = product.Description,
                CategoryId = product.CategoryId,
                ImageUrl = product.ImageUrl
            };

            var categories = await _categoryRepository.GetAllAsync();
            ViewBag.Categories = new SelectList(categories, "Id", "Name", product.CategoryId);
            return View(model);
        }

        // POST: /Product/Update
        [HttpPost]
        [Authorize(Roles = "Admin")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Update(ProductViewModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var product = await _productRepository.GetByIdAsync(model.Id);
                    if (product == null)
                    {
                        TempData["Error"] = "Không tìm thấy sản phẩm!";
                        return RedirectToAction("Index");
                    }

                    // Cập nhật thông tin cơ bản
                    product.Name = model.Name;
                    product.Price = model.Price;
                    product.Description = model.Description;
                    product.CategoryId = model.CategoryId;

                    // Xử lý hình ảnh
                    if (model.ImageFile != null)
                    {
                        // Xóa hình ảnh cũ nếu có
                        _fileUploadService.DeleteImage(product.ImageUrl);
                        // Upload hình ảnh mới
                        product.ImageUrl = await _fileUploadService.UploadImageAsync(model.ImageFile);
                    }
                    else if (!string.IsNullOrEmpty(model.ImageUrlInput) && model.ImageUrlInput != product.ImageUrl)
                    {
                        // Nếu thay đổi URL và URL cũ là file local thì xóa
                        if (!string.IsNullOrEmpty(product.ImageUrl) && product.ImageUrl.StartsWith("/images/"))
                        {
                            _fileUploadService.DeleteImage(product.ImageUrl);
                        }
                        product.ImageUrl = model.ImageUrlInput;
                    }

                    await _productRepository.UpdateAsync(product);
                    TempData["Success"] = "Cập nhật sản phẩm thành công!";
                    return RedirectToAction("Index");
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", ex.Message);
                }
            }

            // Reload categories nếu có lỗi
            var categories = await _categoryRepository.GetAllAsync();
            ViewBag.Categories = new SelectList(categories, "Id", "Name", model.CategoryId);
            return View(model);
        }

        // GET: /Product/Delete - Chỉ Admin mới được xóa sản phẩm
        [HttpGet]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Delete(int id)
        {
            var product = await _productRepository.GetByIdAsync(id);
            if (product == null)
            {
                TempData["Error"] = "Không tìm thấy sản phẩm!";
                return RedirectToAction("Index");
            }
            return View(product);
        }

        // POST: /Product/Delete
        [HttpPost]
        [Authorize(Roles = "Admin")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var product = await _productRepository.GetByIdAsync(id);
            if (product != null)
            {
                // Xóa hình ảnh nếu có
                _fileUploadService.DeleteImage(product.ImageUrl);

                await _productRepository.DeleteAsync(id);
                TempData["Success"] = "Xóa sản phẩm thành công!";
            }
            else
            {
                TempData["Error"] = "Không tìm thấy sản phẩm!";
            }

            return RedirectToAction("Index");
        }
    }
}
