﻿@model THLTW_B2.Models.ViewModels.ProductViewModel
@{
    ViewData["Title"] = "Thêm sản phẩm mới";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<style>
    .aurora-product-form-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .aurora-product-form-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .aurora-form-section {
        padding: 2rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .aurora-form-section:last-child {
        border-bottom: none;
    }

    .aurora-section-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #1a1d29;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .aurora-section-icon {
        width: 40px;
        height: 40px;
        background: var(--aurora-primary);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1rem;
    }

    .aurora-image-tabs {
        background: rgba(102, 126, 234, 0.05);
        border-radius: 12px;
        padding: 0.5rem;
        margin-bottom: 1rem;
    }

    .aurora-image-tabs .nav-link {
        border: none;
        border-radius: 8px;
        color: #64748b;
        font-weight: 600;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .aurora-image-tabs .nav-link.active {
        background: white;
        color: #667eea;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .aurora-image-tabs .nav-link:hover {
        color: #667eea;
    }

    .aurora-file-upload {
        border: 2px dashed rgba(102, 126, 234, 0.3);
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        background: rgba(102, 126, 234, 0.02);
        transition: all 0.3s ease;
    }

    .aurora-file-upload:hover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.05);
    }

    .aurora-file-upload-icon {
        font-size: 3rem;
        color: #667eea;
        margin-bottom: 1rem;
    }

    .aurora-form-actions {
        padding: 2rem;
        background: rgba(248, 250, 252, 0.5);
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
    }

    @media (max-width: 768px) {
        .aurora-form-actions {
            flex-direction: column;
        }

        .aurora-form-actions .btn {
            width: 100%;
        }
    }
</style>

<!-- Header Section -->
<div class="aurora-product-form-header">
    <div class="d-flex flex-column flex-md-row align-items-md-center justify-content-between">
        <div>
            <h1 style="font-size: 2.5rem; font-weight: 800; color: #1a1d29; margin-bottom: 0.5rem;">
                <i class="fas fa-plus me-3" style="color: #667eea;"></i>Thêm sản phẩm mới
            </h1>
            <p style="color: #64748b; font-size: 1.1rem; margin: 0;">
                Tạo sản phẩm mới và thêm vào danh mục của bạn
            </p>
        </div>
        <div class="mt-3 mt-md-0">
            <a asp-action="Index" class="aurora-btn aurora-btn-outline">
                <i class="fas fa-arrow-left me-2"></i>Quay lại danh sách
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="aurora-product-form-card">
            <form asp-action="Add" method="post" enctype="multipart/form-data">
                <!-- Validation Summary -->
                <div asp-validation-summary="All" class="aurora-alert aurora-alert-danger" style="margin: 2rem;"></div>

                <!-- Basic Information Section -->
                <div class="aurora-form-section">
                    <div class="aurora-section-title">
                        <div class="aurora-section-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <span>Thông tin cơ bản</span>
                    </div>

                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="aurora-form-group">
                                <label asp-for="Name" class="aurora-form-label">
                                    <i class="fas fa-tag me-2"></i>Tên sản phẩm <span style="color: #ef4444;">*</span>
                                </label>
                                <input asp-for="Name" class="aurora-form-input" placeholder="Nhập tên sản phẩm..." />
                                <span asp-validation-for="Name" class="text-danger small"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="aurora-form-group">
                                <label asp-for="Price" class="aurora-form-label">
                                    <i class="fas fa-dollar-sign me-2"></i>Giá bán <span style="color: #ef4444;">*</span>
                                </label>
                                <div class="input-group">
                                    <input asp-for="Price" class="aurora-form-input" placeholder="0" type="number" step="1000" min="0" style="border-top-right-radius: 0; border-bottom-right-radius: 0;" />
                                    <span class="input-group-text" style="background: rgba(102, 126, 234, 0.1); border: 2px solid rgba(102, 126, 234, 0.1); border-left: none; color: #667eea; font-weight: 600; border-top-right-radius: 12px; border-bottom-right-radius: 12px;">VNĐ</span>
                                </div>
                                <span asp-validation-for="Price" class="text-danger small"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="aurora-form-group">
                                <label asp-for="CategoryId" class="aurora-form-label">
                                    <i class="fas fa-list me-2"></i>Danh mục <span style="color: #ef4444;">*</span>
                                </label>
                                <select asp-for="CategoryId" asp-items="ViewBag.Categories" class="aurora-form-select">
                                    <option value="">-- Chọn danh mục sản phẩm --</option>
                                </select>
                                <span asp-validation-for="CategoryId" class="text-danger small"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="aurora-form-group">
                                <label asp-for="Description" class="aurora-form-label">
                                    <i class="fas fa-align-left me-2"></i>Mô tả ngắn
                                </label>
                                <textarea asp-for="Description" class="aurora-form-textarea" rows="3" placeholder="Nhập mô tả ngắn gọn về sản phẩm..." style="min-height: 80px;"></textarea>
                                <span asp-validation-for="Description" class="text-danger small"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Image Upload Section -->
                <div class="aurora-form-section">
                    <div class="aurora-section-title">
                        <div class="aurora-section-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <span>Hình ảnh sản phẩm</span>
                    </div>

                    <!-- Tab navigation -->
                    <ul class="nav nav-tabs aurora-image-tabs" id="imageTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload" type="button" role="tab">
                                <i class="fas fa-upload me-2"></i>Upload File
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="url-tab" data-bs-toggle="tab" data-bs-target="#url" type="button" role="tab">
                                <i class="fas fa-link me-2"></i>Nhập URL
                            </button>
                        </li>
                    </ul>

                    <!-- Tab content -->
                    <div class="tab-content" id="imageTabContent">
                        <div class="tab-pane fade show active" id="upload" role="tabpanel">
                            <div class="aurora-file-upload">
                                <div class="aurora-file-upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h5 style="color: #1a1d29; font-weight: 600; margin-bottom: 0.5rem;">Chọn file hình ảnh</h5>
                                <p style="color: #64748b; margin-bottom: 1.5rem;">Kéo thả file vào đây hoặc click để chọn</p>
                                <input asp-for="ImageFile" type="file" class="aurora-form-input" accept="image/*" style="max-width: 300px; margin: 0 auto;" />
                                <span asp-validation-for="ImageFile" class="text-danger small d-block mt-2"></span>
                                <small style="color: #94a3b8; margin-top: 1rem; display: block;">
                                    Hỗ trợ: JPG, PNG, GIF, WEBP • Tối đa 5MB
                                </small>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="url" role="tabpanel">
                            <div class="aurora-form-group" style="margin-top: 1rem;">
                                <label class="aurora-form-label">
                                    <i class="fas fa-globe me-2"></i>URL hình ảnh
                                </label>
                                <input asp-for="ImageUrlInput" class="aurora-form-input" placeholder="https://example.com/image.jpg" />
                                <span asp-validation-for="ImageUrlInput" class="text-danger small"></span>
                                <small style="color: #94a3b8; margin-top: 0.5rem; display: block;">
                                    Nhập đường dẫn URL trực tiếp đến hình ảnh sản phẩm
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="aurora-form-actions">
                    <a asp-action="Index" class="aurora-btn aurora-btn-outline">
                        <i class="fas fa-arrow-left me-2"></i>Quay lại danh sách
                    </a>
                    <div class="d-flex gap-3">
                        <button type="reset" class="aurora-btn" style="background: rgba(148, 163, 184, 0.1); color: #64748b; border: 2px solid rgba(148, 163, 184, 0.2);">
                            <i class="fas fa-undo me-2"></i>Đặt lại
                        </button>
                        <button type="submit" class="aurora-btn aurora-btn-success">
                            <i class="fas fa-plus me-2"></i>Thêm sản phẩm
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
