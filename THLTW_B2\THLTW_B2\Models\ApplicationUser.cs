using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace THLTW_B2.Models
{
    public class ApplicationUser : IdentityUser
    {
        [Required]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public bool IsActive { get; set; } = true;

        public string? AvatarUrl { get; set; } // Đường dẫn ảnh đại diện
    }
}
