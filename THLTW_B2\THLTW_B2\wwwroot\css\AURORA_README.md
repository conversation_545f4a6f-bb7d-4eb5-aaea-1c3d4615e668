# Aurora Design System

Aurora là một design system hiện đại được thiết kế cho ứng dụng ASP.NET Core MVC, mang đến trải nghiệm người dùng sang trọng với hiệu ứng glass morphism và gradient đẹp mắt.

## 🎨 Đặc điểm chính

- **Glass Morphism**: Hiệu <PERSON>ng kính mờ với backdrop-filter
- **Gradient Backgrounds**: Màu gradient đẹp mắt và hiện đại
- **Responsive Design**: Tương thích với mọi thiết bị
- **Smooth Animations**: Hiệu ứng chuyển động mượt mà
- **Consistent Typography**: Font chữ Inter đồng nhất
- **Accessible**: <PERSON><PERSON> thủ các tiêu chuẩn accessibility

## 🎯 Color Palette

### Primary Colors
- `--aurora-primary`: Linear gradient từ #667eea đến #764ba2
- `--aurora-secondary`: Linear gradient từ #f093fb đến #f5576c
- `--aurora-success`: Linear gradient từ #4facfe đến #00f2fe
- `--aurora-warning`: Linear gradient từ #43e97b đến #38f9d7
- `--aurora-danger`: Linear gradient từ #ff6b6b đến #ee5a52

### Neutral Colors
- `--aurora-dark`: #1a1d29
- `--aurora-light`: #f8fafc
- `--aurora-text`: #64748b
- `--aurora-text-dark`: #334155

## 🧩 Components

### Cards
```html
<div class="aurora-card">
    <div class="aurora-card-header">
        <h3>Card Title</h3>
    </div>
    <div class="aurora-card-body">
        Card content goes here
    </div>
</div>
```

### Buttons
```html
<button class="aurora-btn aurora-btn-primary">
    <i class="fas fa-star"></i>
    Primary Button
</button>
```

**Variants:**
- `aurora-btn-primary`
- `aurora-btn-secondary`
- `aurora-btn-success`
- `aurora-btn-warning`
- `aurora-btn-danger`
- `aurora-btn-outline`

### Stat Cards
```html
<div class="aurora-stat-card">
    <div class="aurora-stat-icon" style="background: var(--aurora-primary);">
        <i class="fas fa-users"></i>
    </div>
    <div class="aurora-stat-label">Total Users</div>
    <div class="aurora-stat-value">2,847</div>
    <div class="aurora-stat-trend up">
        <i class="fas fa-arrow-up"></i>+12.5%
    </div>
</div>
```

### Forms
```html
<div class="aurora-form-group">
    <label class="aurora-form-label">Email Address</label>
    <input type="email" class="aurora-form-input" placeholder="Enter email">
</div>
```

### Alerts
```html
<div class="aurora-alert aurora-alert-success">
    <i class="fas fa-check-circle"></i>
    <div>Success message here</div>
</div>
```

**Variants:**
- `aurora-alert-success`
- `aurora-alert-danger`
- `aurora-alert-warning`
- `aurora-alert-info`

### Badges
```html
<span class="aurora-badge">Primary</span>
<span class="aurora-badge aurora-badge-success">Success</span>
```

### Tables
```html
<table class="aurora-table">
    <thead>
        <tr>
            <th>Column 1</th>
            <th>Column 2</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Data 1</td>
            <td>Data 2</td>
        </tr>
    </tbody>
</table>
```

## 📱 Responsive Breakpoints

- **Mobile**: < 576px
- **Tablet**: 576px - 768px
- **Desktop**: > 768px

## 🚀 Usage

1. Include Aurora CSS trong layout:
```html
<link rel="stylesheet" href="~/css/aurora.css" />
```

2. Sử dụng các class components:
```html
<div class="aurora-card">
    <div class="aurora-card-body">
        <button class="aurora-btn aurora-btn-primary">Click me</button>
    </div>
</div>
```

## 🎨 Customization

Bạn có thể tùy chỉnh Aurora bằng cách override CSS variables:

```css
:root {
    --aurora-primary: linear-gradient(135deg, #your-color-1, #your-color-2);
    --aurora-radius-md: 16px;
    --aurora-spacing-md: 1.5rem;
}
```

## 📋 Best Practices

1. **Consistency**: Luôn sử dụng Aurora components thay vì tự tạo styles
2. **Spacing**: Sử dụng Aurora spacing variables
3. **Colors**: Chỉ sử dụng Aurora color palette
4. **Typography**: Sử dụng Inter font family
5. **Accessibility**: Luôn include proper ARIA labels và semantic HTML

## 🔧 Development

Aurora được phát triển với:
- CSS3 với CSS Variables
- Flexbox và CSS Grid
- Modern browser features (backdrop-filter, etc.)
- Mobile-first responsive design

## 📄 License

Aurora Design System được phát triển cho dự án THLTW_B2.

---

**Phiên bản**: 1.0.0  
**Tác giả**: Aurora Team  
**Cập nhật**: 2024
