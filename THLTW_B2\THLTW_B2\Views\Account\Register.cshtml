@model THLTW_B2.Models.ViewModels.RegisterViewModel
@{
    ViewData["Title"] = "Đăng ký";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="row justify-content-center align-items-center min-vh-100 luxury-register-bg">
    <!-- Background decorative elements -->
    <div class="bg-decoration bg-decoration-1"></div>
    <div class="bg-decoration bg-decoration-2"></div>
    <div class="bg-decoration bg-decoration-3"></div>
    
    <div class="col-md-8 col-lg-6 col-xl-5">
        <div class="card luxury-register-card border-0 shadow-luxury">
            <!-- Header Section -->
            <div class="card-header bg-transparent border-0 text-center py-4">
                <div class="luxury-icon-wrapper mb-3">
                    <i class="fas fa-user-plus luxury-icon"></i>
                </div>
                <h1 class="luxury-title mb-2">Tạo Tài <PERSON>hoản Mới</h1>
                <p class="luxury-subtitle">Tham gia cộng đồng của chúng tôi ngay hôm nay</p>
            </div>
            
            <!-- Form Section -->
            <div class="card-body px-4 pb-4">
                <form asp-action="Register" asp-route-returnurl="@ViewData["ReturnUrl"]" method="post" enctype="multipart/form-data" autocomplete="off">
                    <div asp-validation-summary="All" class="alert luxury-alert mb-4" role="alert"></div>
                    
                    <!-- Full Name Field -->
                    <div class="mb-4">
                        <label asp-for="FullName" class="form-label luxury-label">
                            <i class="fas fa-user me-2"></i>Họ và Tên
                        </label>
                        <div class="luxury-input-wrapper">
                            <input asp-for="FullName" class="form-control luxury-input" 
                                   placeholder="Nguyễn Văn A" />
                            <div class="luxury-input-border"></div>
                        </div>
                        <span asp-validation-for="FullName" class="luxury-error"></span>
                    </div>
                    
                    <!-- Email Field -->
                    <div class="mb-4">
                        <label asp-for="Email" class="form-label luxury-label">
                            <i class="fas fa-envelope me-2"></i>Địa chỉ Email
                        </label>
                        <div class="luxury-input-wrapper">
                            <input asp-for="Email" class="form-control luxury-input" 
                                   placeholder="<EMAIL>" />
                            <div class="luxury-input-border"></div>
                        </div>
                        <span asp-validation-for="Email" class="luxury-error"></span>
                    </div>
                    
                    <!-- Phone Number Field -->
                    <div class="mb-4">
                        <label asp-for="PhoneNumber" class="form-label luxury-label">
                            <i class="fas fa-phone me-2"></i>Số Điện Thoại
                            <span class="luxury-optional">(Tùy chọn)</span>
                        </label>
                        <div class="luxury-input-wrapper">
                            <input asp-for="PhoneNumber" class="form-control luxury-input" 
                                   placeholder="0901 234 567" />
                            <div class="luxury-input-border"></div>
                        </div>
                        <span asp-validation-for="PhoneNumber" class="luxury-error"></span>
                    </div>
                    
                    <!-- Password Fields Row -->
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <label asp-for="Password" class="form-label luxury-label">
                                <i class="fas fa-lock me-2"></i>Mật khẩu
                            </label>
                            <div class="luxury-input-wrapper">
                                <input asp-for="Password" class="form-control luxury-input" 
                                       placeholder="Ít nhất 6 ký tự" />
                                <div class="luxury-input-border"></div>
                            </div>
                            <span asp-validation-for="Password" class="luxury-error"></span>
                        </div>
                        
                        <div class="col-md-6 mb-4">
                            <label asp-for="ConfirmPassword" class="form-label luxury-label">
                                <i class="fas fa-shield-alt me-2"></i>Xác nhận
                            </label>
                            <div class="luxury-input-wrapper">
                                <input asp-for="ConfirmPassword" class="form-control luxury-input" 
                                       placeholder="Nhập lại mật khẩu" />
                                <div class="luxury-input-border"></div>
                            </div>
                            <span asp-validation-for="ConfirmPassword" class="luxury-error"></span>
                        </div>
                    </div>
                    
                    <!-- Avatar Upload -->
                    <div class="mb-4">
                        <label class="form-label luxury-label">
                            <i class="fas fa-camera me-2"></i>Ảnh Đại Diện
                            <span class="luxury-optional">(Tùy chọn)</span>
                        </label>
                        <div class="luxury-file-upload">
                            <input type="file" name="Avatar" class="luxury-file-input" accept="image/*" id="avatarInput" />
                            <label for="avatarInput" class="luxury-file-label">
                                <div class="luxury-file-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="luxury-file-text">
                                    <span class="luxury-file-title">Chọn ảnh đại diện</span>
                                    <span class="luxury-file-subtitle">PNG, JPG tối đa 5MB</span>
                                </div>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Terms & Conditions -->
                    <div class="mb-4">
                        <div class="form-check luxury-checkbox-wrapper">
                            <input type="checkbox" class="form-check-input luxury-checkbox" id="agreeTerms" required />
                            <label class="form-check-label luxury-checkbox-label" for="agreeTerms">
                                Tôi đồng ý với 
                                <a href="#" class="luxury-link">Điều khoản sử dụng</a> 
                                và 
                                <a href="#" class="luxury-link">Chính sách bảo mật</a>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Register Button -->
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn luxury-btn-primary">
                            <span class="btn-text">
                                <i class="fas fa-user-plus me-2"></i>Tạo Tài Khoản
                            </span>
                            <div class="btn-shine"></div>
                        </button>
                    </div>
                </form>
                
                <!-- Login Link -->
                <div class="text-center">
                    <p class="luxury-text-muted mb-0">
                        Đã có tài khoản? 
                        <a href="@Url.Action("Login", "Account", new { returnUrl = ViewData["ReturnUrl"] })" 
                           class="luxury-link">Đăng nhập ngay</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}