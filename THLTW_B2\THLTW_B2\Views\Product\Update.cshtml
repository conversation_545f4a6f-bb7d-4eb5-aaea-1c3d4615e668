﻿@model THLTW_B2.Models.ViewModels.ProductViewModel
@{
    ViewData["Title"] = "Chỉnh sửa sản phẩm";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-edit"></i> @ViewData["Title"]</h3>
            </div>
            <div class="card-body">
                <form asp-action="Update" method="post" enctype="multipart/form-data">
                    <input type="hidden" asp-for="Id" />
                    <div asp-validation-summary="All" class="text-danger mb-3"></div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Name" class="form-label">Tên sản phẩm <span class="text-danger">*</span></label>
                                <input asp-for="Name" class="form-control" placeholder="Nhập tên sản phẩm" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Price" class="form-label">Giá <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input asp-for="Price" class="form-control" placeholder="0" type="number" step="0.01" min="0" />
                                    <span class="input-group-text">VNĐ</span>
                                </div>
                                <span asp-validation-for="Price" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="CategoryId" class="form-label">Danh mục <span class="text-danger">*</span></label>
                                <select asp-for="CategoryId" asp-items="ViewBag.Categories" class="form-select">
                                    <option value="">-- Chọn danh mục --</option>
                                </select>
                                <span asp-validation-for="CategoryId" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Hình ảnh sản phẩm</label>

                                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                                {
                                    <div class="mb-2">
                                        <img src="@Model.ImageUrl" alt="Current image" class="img-thumbnail" style="max-width: 150px; max-height: 150px;" />
                                        <small class="d-block text-muted">Hình ảnh hiện tại</small>
                                    </div>
                                }

                                <!-- Tab navigation -->
                                <ul class="nav nav-tabs" id="imageTab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload" type="button" role="tab">
                                            <i class="fas fa-upload"></i> Upload file mới
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="url-tab" data-bs-toggle="tab" data-bs-target="#url" type="button" role="tab">
                                            <i class="fas fa-link"></i> Thay đổi URL
                                        </button>
                                    </li>
                                </ul>

                                <!-- Tab content -->
                                <div class="tab-content mt-2" id="imageTabContent">
                                    <div class="tab-pane fade show active" id="upload" role="tabpanel">
                                        <input asp-for="ImageFile" type="file" class="form-control" accept="image/*" />
                                        <span asp-validation-for="ImageFile" class="text-danger"></span>
                                        <small class="form-text text-muted">Chọn file hình ảnh mới (JPG, PNG, GIF, WEBP - tối đa 5MB)</small>
                                    </div>
                                    <div class="tab-pane fade" id="url" role="tabpanel">
                                        <input asp-for="ImageUrlInput" class="form-control" placeholder="https://example.com/image.jpg" value="@Model.ImageUrl" />
                                        <span asp-validation-for="ImageUrlInput" class="text-danger"></span>
                                        <small class="form-text text-muted">Nhập đường dẫn URL mới của hình ảnh sản phẩm</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-4">
                        <label asp-for="Description" class="form-label">Mô tả sản phẩm</label>
                        <textarea asp-for="Description" class="form-control" rows="4" placeholder="Nhập mô tả chi tiết về sản phẩm..."></textarea>
                        <span asp-validation-for="Description" class="text-danger"></span>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save"></i> Cập nhật sản phẩm
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}