@{
    ViewData["Title"] = "Tài khoản bị khóa";
}
<div class="row justify-content-center align-items-center min-vh-100" style="background: linear-gradient(135deg, #fffbe6 0%, #ffe6e6 100%);">
    <div class="col-md-7 col-lg-5">
        <div class="card shadow-lg border-0 rounded-4">
            <div class="card-body text-center p-5">
                <div class="mb-4">
                    <i class="fas fa-user-lock text-danger" style="font-size: 5rem;"></i>
                </div>
                <h2 class="text-danger fw-bold">Tài khoản của bạn đã bị khóa</h2>
                <p class="lead">Bạn đã đăng nhập sai quá nhiều lần hoặc bị quản trị viên khóa tài khoản.</p>
                <p>Vui lòng thử lại sau hoặc liên hệ quản trị viên để được hỗ trợ.</p>
                <div class="mt-4 d-flex justify-content-center gap-3">
                    <a asp-controller="Home" asp-action="Index" class="btn btn-primary btn-lg rounded-pill">
                        <i class="fas fa-home me-1"></i> Về trang chủ
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
