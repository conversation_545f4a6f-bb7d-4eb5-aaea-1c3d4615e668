@model List<THLTW_B2.Controllers.UserViewModel>
@{
    ViewData["Title"] = "<PERSON>uản lý người dùng";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<style>
    .aurora-users-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .aurora-users-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .aurora-users-table {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: none;
        border: none;
    }

    .aurora-users-table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1.25rem 1rem;
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .aurora-users-table tbody td {
        padding: 1.25rem 1rem;
        border: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        vertical-align: middle;
    }

    .aurora-users-table tbody tr:hover {
        background: rgba(102, 126, 234, 0.05);
        transform: scale(1.01);
        transition: all 0.3s ease;
    }

    .aurora-avatar {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        object-fit: cover;
        border: 3px solid transparent;
        background: linear-gradient(white, white) padding-box, var(--aurora-primary) border-box;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .aurora-user-name {
        font-weight: 700;
        color: #1a1d29;
        font-size: 1rem;
    }

    .aurora-user-email {
        color: #64748b;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .aurora-role-badge {
        padding: 0.375rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-right: 0.25rem;
    }

    .aurora-role-admin {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }

    .aurora-role-user {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .aurora-status-badge {
        padding: 0.375rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .aurora-status-active {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }

    .aurora-status-inactive {
        background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
        color: white;
    }

    .aurora-action-btn {
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
        border: none;
        font-size: 0.75rem;
        font-weight: 600;
        margin: 0 0.125rem;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .aurora-btn-toggle-active {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    }

    .aurora-btn-toggle-inactive {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(67, 233, 123, 0.3);
    }

    .aurora-btn-promote {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
    }

    .aurora-btn-demote {
        background: transparent;
        color: #f5576c;
        border: 2px solid #f5576c;
    }

    .aurora-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .aurora-empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #64748b;
    }

    .aurora-empty-icon {
        font-size: 4rem;
        color: #cbd5e1;
        margin-bottom: 1.5rem;
    }
</style>

<div class="aurora-users-header">
    <div class="d-flex flex-column flex-md-row align-items-md-center justify-content-between">
        <div>
            <h1 style="font-size: 2.5rem; font-weight: 800; color: #1a1d29; margin-bottom: 0.5rem;">
                <i class="fas fa-users me-3" style="color: #667eea;"></i>Quản lý người dùng
            </h1>
            <p style="color: #64748b; font-size: 1.1rem; margin: 0;">
                Quản lý tài khoản và phân quyền người dùng trong hệ thống
            </p>
        </div>
        <div class="mt-3 mt-md-0">
            <a asp-controller="Admin" asp-action="Index" class="aurora-btn aurora-btn-outline">
                <i class="fas fa-arrow-left me-2"></i>Quay lại Dashboard
            </a>
        </div>
    </div>
</div>

@if (TempData["Success"] != null)
{
    <div class="aurora-alert aurora-alert-success">
        <i class="fas fa-check-circle"></i>
        <div>@TempData["Success"]</div>
    </div>
}

@if (TempData["Error"] != null)
{
    <div class="aurora-alert aurora-alert-danger">
        <i class="fas fa-exclamation-circle"></i>
        <div>@TempData["Error"]</div>
    </div>
}

@if (TempData["Info"] != null)
{
    <div class="aurora-alert aurora-alert-info">
        <i class="fas fa-info-circle"></i>
        <div>@TempData["Info"]</div>
    </div>
}

<div class="aurora-users-card">
    <div class="aurora-card-body p-0">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table aurora-users-table mb-0">
                    <thead>
                        <tr>
                            <th>Avatar</th>
                            <th>Thông tin</th>
                            <th>Liên hệ</th>
                            <th>Vai trò</th>
                            <th>Trạng thái</th>
                            <th>Ngày tạo</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var user in Model)
                        {
                            <tr>
                                <td>
                                    <img src="@(user.AvatarUrl ?? Url.Content("~/images/placeholder.svg"))"
                                         alt="@user.FullName"
                                         class="aurora-avatar" />
                                </td>
                                <td>
                                    <div class="aurora-user-name">@user.FullName</div>
                                    <div class="aurora-user-email">@user.Email</div>
                                </td>
                                <td>
                                    <div style="color: #64748b;">
                                        <i class="fas fa-phone me-1"></i>
                                        @(user.PhoneNumber ?? "Chưa có")
                                    </div>
                                </td>
                                <td>
                                    @if (user.Roles.Contains("Admin"))
                                    {
                                        <span class="aurora-role-badge aurora-role-admin">
                                            <i class="fas fa-crown me-1"></i>Admin
                                        </span>
                                    }
                                    @if (user.Roles.Contains("User"))
                                    {
                                        <span class="aurora-role-badge aurora-role-user">
                                            <i class="fas fa-user me-1"></i>User
                                        </span>
                                    }
                                </td>
                                <td>
                                    @if (user.IsActive)
                                    {
                                        <span class="aurora-status-badge aurora-status-active">
                                            <i class="fas fa-check-circle me-1"></i>Hoạt động
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="aurora-status-badge aurora-status-inactive">
                                            <i class="fas fa-ban me-1"></i>Vô hiệu hóa
                                        </span>
                                    }
                                </td>
                                <td>
                                    <div style="color: #64748b; font-weight: 500;">
                                        <i class="fas fa-calendar me-1"></i>
                                        @user.CreatedDate.ToString("dd/MM/yyyy")
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex flex-wrap gap-1">
                                        <!-- Toggle Status -->
                                        <form asp-action="ToggleUserStatus" method="post" style="display: inline;">
                                            <input type="hidden" name="userId" value="@user.Id" />
                                            <button type="submit"
                                                    class="aurora-action-btn @(user.IsActive ? "aurora-btn-toggle-active" : "aurora-btn-toggle-inactive")"
                                                    onclick="return confirm('@(user.IsActive ? "Vô hiệu hóa" : "Kích hoạt") tài khoản này?')"
                                                    title="@(user.IsActive ? "Vô hiệu hóa tài khoản" : "Kích hoạt tài khoản")">
                                                <i class="fas @(user.IsActive ? "fa-ban" : "fa-check") me-1"></i>
                                                @(user.IsActive ? "Vô hiệu" : "Kích hoạt")
                                            </button>
                                        </form>

                                        <!-- Admin Role Management -->
                                        @if (!user.Roles.Contains("Admin"))
                                        {
                                            <form asp-action="PromoteToAdmin" method="post" style="display: inline;">
                                                <input type="hidden" name="userId" value="@user.Id" />
                                                <button type="submit"
                                                        class="aurora-action-btn aurora-btn-promote"
                                                        onclick="return confirm('Thăng cấp thành Admin?')"
                                                        title="Thăng cấp thành Admin">
                                                    <i class="fas fa-crown me-1"></i>Admin
                                                </button>
                                            </form>
                                        }
                                        else
                                        {
                                            <form asp-action="RemoveFromAdmin" method="post" style="display: inline;">
                                                <input type="hidden" name="userId" value="@user.Id" />
                                                <button type="submit"
                                                        class="aurora-action-btn aurora-btn-demote"
                                                        onclick="return confirm('Gỡ quyền Admin?')"
                                                        title="Gỡ quyền Admin">
                                                    <i class="fas fa-user-minus me-1"></i>Gỡ Admin
                                                </button>
                                            </form>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="aurora-empty-state" style="padding: 4rem 2rem;">
                <div class="aurora-empty-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3 style="color: #1a1d29; font-weight: 700; margin-bottom: 1rem;">
                    Chưa có người dùng nào
                </h3>
                <p style="color: #64748b; font-size: 1.1rem; margin: 0;">
                    Hệ thống chưa có người dùng nào được đăng ký. Người dùng sẽ xuất hiện ở đây sau khi đăng ký tài khoản.
                </p>
            </div>
        }
    </div>
</div>
