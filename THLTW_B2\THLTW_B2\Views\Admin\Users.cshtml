@model List<THLTW_B2.Controllers.UserViewModel>
@{
    ViewData["Title"] = "Quản lý người dùng";
    Layout = "~/Views/Shared/_AdminLayout.cshtml";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>@ViewData["Title"]</h2>
    <a asp-controller="Admin" asp-action="Index" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Quay lại
    </a>
</div>

@if (TempData["Success"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> @TempData["Success"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["Error"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle"></i> @TempData["Error"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["Info"] != null)
{
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="fas fa-info-circle"></i> @TempData["Info"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<div class="card">
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Avatar</th>
                            <th>Họ tên</th>
                            <th>Email</th>
                            <th>Số điện thoại</th>
                            <th>Vai trò</th>
                            <th>Trạng thái</th>
                            <th>Ngày tạo</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var user in Model)
                        {
                            <tr>
                                <td>
                                    <img src="@(user.AvatarUrl ?? Url.Content("~/images/placeholder.svg"))" alt="avatar" class="rounded-circle" style="width:40px;height:40px;object-fit:cover;border:2px solid #ffc107;" />
                                </td>
                                <td>@user.FullName</td>
                                <td>@user.Email</td>
                                <td>@(user.PhoneNumber ?? "Chưa có")</td>
                                <td>
                                    @if (user.Roles.Contains("Admin"))
                                    {
                                        <span class="badge bg-danger">Admin</span>
                                    }
                                    @if (user.Roles.Contains("User"))
                                    {
                                        <span class="badge bg-primary">User</span>
                                    }
                                </td>
                                <td>
                                    @if (user.IsActive)
                                    {
                                        <span class="badge bg-success">Hoạt động</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">Vô hiệu hóa</span>
                                    }
                                </td>
                                <td>@user.CreatedDate.ToString("dd/MM/yyyy")</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <!-- Toggle Status -->
                                        <form asp-action="ToggleUserStatus" method="post" style="display: inline;">
                                            <input type="hidden" name="userId" value="@user.Id" />
                                            <button type="submit" class="btn btn-sm @(user.IsActive ? "btn-warning" : "btn-success")" 
                                                    onclick="return confirm('@(user.IsActive ? "Vô hiệu hóa" : "Kích hoạt") tài khoản này?')">
                                                <i class="fas @(user.IsActive ? "fa-ban" : "fa-check")"></i>
                                                @(user.IsActive ? "Vô hiệu" : "Kích hoạt")
                                            </button>
                                        </form>
                                        
                                        <!-- Admin Role Management -->
                                        @if (!user.Roles.Contains("Admin"))
                                        {
                                            <form asp-action="PromoteToAdmin" method="post" style="display: inline;">
                                                <input type="hidden" name="userId" value="@user.Id" />
                                                <button type="submit" class="btn btn-sm btn-danger" 
                                                        onclick="return confirm('Thăng cấp thành Admin?')">
                                                    <i class="fas fa-user-shield"></i> Admin
                                                </button>
                                            </form>
                                        }
                                        else
                                        {
                                            <form asp-action="RemoveFromAdmin" method="post" style="display: inline;">
                                                <input type="hidden" name="userId" value="@user.Id" />
                                                <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                        onclick="return confirm('Gỡ quyền Admin?')">
                                                    <i class="fas fa-user-minus"></i> Gỡ Admin
                                                </button>
                                            </form>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-4">
                <i class="fas fa-users text-muted" style="font-size: 3rem;"></i>
                <p class="mt-3 text-muted">Chưa có người dùng nào trong hệ thống.</p>
            </div>
        }
    </div>
</div>
