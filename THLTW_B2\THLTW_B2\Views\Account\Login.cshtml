@model THLTW_B2.Models.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "Đăng nhập";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        position: relative;
        overflow-x: hidden;
    }

    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    .aurora-login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 1rem;
    }

    .aurora-login-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 24px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        padding: 3rem;
        width: 100%;
        max-width: 450px;
        animation: fadeInUp 0.8s ease-out;
    }

    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .aurora-login-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .aurora-login-icon {
        width: 80px;
        height: 80px;
        background: var(--aurora-primary);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 2rem;
        color: white;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
    }

    .aurora-login-title {
        font-size: 2rem;
        font-weight: 800;
        color: #1a1d29;
        margin-bottom: 0.5rem;
    }

    .aurora-login-subtitle {
        color: #64748b;
        font-size: 1rem;
    }

    .aurora-form-group {
        margin-bottom: 1.5rem;
    }

    .aurora-form-label {
        color: #1a1d29;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: block;
    }

    .aurora-form-input {
        width: 100%;
        padding: 1rem 1.25rem;
        border: 2px solid rgba(102, 126, 234, 0.1);
        border-radius: 16px;
        background: rgba(248, 250, 252, 0.8);
        font-size: 1rem;
        transition: all 0.3s ease;
        outline: none;
    }

    .aurora-form-input:focus {
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    }

    .aurora-btn-primary {
        width: 100%;
        padding: 1rem;
        background: var(--aurora-primary);
        color: white;
        border: none;
        border-radius: 16px;
        font-size: 1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .aurora-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    }

    .aurora-btn-google {
        width: 100%;
        padding: 1rem;
        background: white;
        color: #1a1d29;
        border: 2px solid rgba(0, 0, 0, 0.1);
        border-radius: 16px;
        font-size: 1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .aurora-btn-google:hover {
        background: #f8fafc;
        border-color: #667eea;
        color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .aurora-checkbox {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin: 1.5rem 0;
    }

    .aurora-checkbox input {
        width: 20px;
        height: 20px;
        accent-color: #667eea;
    }

    .aurora-checkbox label {
        color: #64748b;
        font-weight: 500;
        margin: 0;
    }

    .aurora-link {
        color: #667eea;
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
    }

    .aurora-link:hover {
        color: #764ba2;
    }

    .aurora-alert {
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.2);
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        color: #dc2626;
    }
</style>

<div class="aurora-login-container">
    <div class="aurora-login-card">
        <div class="aurora-login-header">
            <div class="aurora-login-icon">
                <i class="fas fa-gem"></i>
            </div>
            <h1 class="aurora-login-title">Chào mừng trở lại</h1>
            <p class="aurora-login-subtitle">Đăng nhập vào Aurora Admin</p>
        </div>

        <form asp-action="Login" asp-route-returnurl="@ViewData["ReturnUrl"]" method="post" autocomplete="off">
            <div asp-validation-summary="All" class="aurora-alert" role="alert"></div>

            <div class="aurora-form-group">
                <label asp-for="Email" class="aurora-form-label">
                    <i class="fas fa-envelope me-2"></i>Email
                </label>
                <input asp-for="Email" class="aurora-form-input" placeholder="Nhập địa chỉ email của bạn" />
                <span asp-validation-for="Email" class="text-danger small"></span>
            </div>

            <div class="aurora-form-group">
                <label asp-for="Password" class="aurora-form-label">
                    <i class="fas fa-lock me-2"></i>Mật khẩu
                </label>
                <input asp-for="Password" class="aurora-form-input" placeholder="Nhập mật khẩu của bạn" />
                <span asp-validation-for="Password" class="text-danger small"></span>
            </div>

            <div class="aurora-checkbox">
                <input asp-for="RememberMe" type="checkbox" />
                <label asp-for="RememberMe">Ghi nhớ đăng nhập</label>
            </div>

            <button type="submit" class="aurora-btn-primary">
                <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập
            </button>
        </form>

        <div style="margin: 1.5rem 0; text-align: center; color: #94a3b8;">
            <span>hoặc</span>
        </div>

        <a href="@Url.Action("ExternalLogin", "Account", new { provider = "Google", returnUrl = ViewData["ReturnUrl"] })" class="aurora-btn-google">
            <i class="fab fa-google"></i>
            Đăng nhập với Google
        </a>

        <div style="text-align: center; margin-top: 2rem; color: #64748b;">
            Chưa có tài khoản?
            <a href="@Url.Action("Register", "Account", new { returnUrl = ViewData["ReturnUrl"] })" class="aurora-link">
                Đăng ký ngay
            </a>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
